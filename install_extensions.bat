@echo off
chcp 65001 >nul
echo === PHP扩展配置脚本 (Windows) ===
echo.

REM 检查PHP是否可用
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP未找到，请确保PHP在PATH中
    pause
    exit /b 1
)

echo ✅ 检测到PHP版本:
php --version | findstr "PHP"
echo.

REM 获取PHP配置信息
echo === 获取PHP配置信息 ===
for /f "tokens=2 delims=:" %%i in ('php --ini ^| findstr "Loaded Configuration File"') do set PHP_INI=%%i
set PHP_INI=%PHP_INI:~1%
echo PHP配置文件: %PHP_INI%

for /f "tokens=1" %%i in ('php -r "echo dirname(PHP_BINARY);"') do set PHP_DIR=%%i
echo PHP目录: %PHP_DIR%
echo.

REM 检查扩展状态
echo === 检查扩展状态 ===
set MISSING_EXTENSIONS=

php -m | findstr /i "mbstring" >nul
if errorlevel 1 (
    echo ❌ mbstring: 未安装
    set MISSING_EXTENSIONS=%MISSING_EXTENSIONS% mbstring
) else (
    echo ✅ mbstring: 已安装
)

php -m | findstr /i "fileinfo" >nul
if errorlevel 1 (
    echo ❌ fileinfo: 未安装
    set MISSING_EXTENSIONS=%MISSING_EXTENSIONS% fileinfo
) else (
    echo ✅ fileinfo: 已安装
)

php -m | findstr /i "gd" >nul
if errorlevel 1 (
    echo ❌ gd: 未安装
    set MISSING_EXTENSIONS=%MISSING_EXTENSIONS% gd
) else (
    echo ✅ gd: 已安装
)

php -m | findstr /i "zip" >nul
if errorlevel 1 (
    echo ❌ zip: 未安装
    set MISSING_EXTENSIONS=%MISSING_EXTENSIONS% zip
) else (
    echo ✅ zip: 已安装
)

if "%MISSING_EXTENSIONS%"=="" (
    echo.
    echo 🎉 所有扩展都已安装！
    echo 您可以安装PhpSpreadsheet了:
    echo composer require phpoffice/phpspreadsheet
    pause
    exit /b 0
)

echo.
echo === 配置解决方案 ===

REM 检查扩展文件是否存在
set EXT_DIR=%PHP_DIR%\ext
echo 扩展目录: %EXT_DIR%
echo.

set INI_ADDITIONS=
if exist "%EXT_DIR%\php_mbstring.dll" (
    echo ✅ 找到: php_mbstring.dll
    set INI_ADDITIONS=%INI_ADDITIONS%extension=mbstring%NEWLINE%
) else (
    echo ❌ 缺失: php_mbstring.dll
)

if exist "%EXT_DIR%\php_fileinfo.dll" (
    echo ✅ 找到: php_fileinfo.dll
    set INI_ADDITIONS=%INI_ADDITIONS%extension=fileinfo%NEWLINE%
) else (
    echo ❌ 缺失: php_fileinfo.dll
)

if exist "%EXT_DIR%\php_gd.dll" (
    echo ✅ 找到: php_gd.dll
    set INI_ADDITIONS=%INI_ADDITIONS%extension=gd%NEWLINE%
) else (
    echo ❌ 缺失: php_gd.dll
)

if exist "%EXT_DIR%\php_zip.dll" (
    echo ✅ 找到: php_zip.dll
    set INI_ADDITIONS=%INI_ADDITIONS%extension=zip%NEWLINE%
) else (
    echo ❌ 缺失: php_zip.dll
)

echo.
echo === 生成配置文件 ===

REM 创建配置建议文件
echo # 请将以下内容添加到 php.ini 文件中 > php_extensions_config.txt
echo # 文件位置: %PHP_INI% >> php_extensions_config.txt
echo. >> php_extensions_config.txt
echo extension=mbstring >> php_extensions_config.txt
echo extension=fileinfo >> php_extensions_config.txt
echo extension=gd >> php_extensions_config.txt
echo extension=zip >> php_extensions_config.txt

echo ✅ 已生成 php_extensions_config.txt 配置文件
echo.

echo === 手动配置步骤 ===
echo 1. 用文本编辑器打开: %PHP_INI%
echo 2. 在文件末尾添加以下内容:
echo    extension=mbstring
echo    extension=fileinfo
echo    extension=gd
echo    extension=zip
echo 3. 保存文件并重启Web服务器
echo 4. 运行验证: php -m ^| findstr "mbstring gd zip fileinfo"
echo.

echo === 备用方案 ===
echo 如果无法配置扩展，您仍然可以使用:
echo 1. CSV导出功能 (完全可用)
echo 2. 简单Excel导出 (HTML格式，Excel兼容)
echo 3. 测试命令: php test_export_detailed.php
echo.

pause
