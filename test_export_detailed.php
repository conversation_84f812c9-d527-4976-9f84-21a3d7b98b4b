<?php
/**
 * 详细测试脚本 - 测试所有导出功能
 * 运行方式: php test_export_detailed.php
 */

require_once 'vendor/autoload.php';

use app\common\service\ExportService;

// 模拟更多测试数据
$testData = [];
for ($i = 0; $i < 10; $i++) {
    $testData[] = [
        'time' => date('Y-m-d H:i:s', strtotime('2024-01-01 10:00:00') + ($i * 600)), // 每10分钟一条
        'layer' => 'Layer-' . ($i % 3 + 1),
        'flow' => round(150 + sin($i) * 20, 1),
        'innerPressure' => round(12 + cos($i) * 2, 2),
        'outerPressure' => round(8 + sin($i * 0.5) * 1.5, 2),
        'temperature' => round(45 + cos($i * 0.3) * 5, 1),
        'valveOpening' => round(75 + sin($i * 0.7) * 10, 1)
    ];
}

// 表头配置
$headers = [
    'time' => '时间',
    'layer' => '层级',
    'flow' => '流量 (L/min)',
    'innerPressure' => '内压 (MPa)',
    'outerPressure' => '外压 (MPa)',
    'temperature' => '温度 (°C)',
    'valveOpening' => '阀门开度 (%)'
];

try {
    $exportService = new ExportService();
    
    echo "=== 设备历史数据导出功能测试 ===\n\n";
    
    // 1. 测试CSV导出
    echo "1. 测试CSV导出功能...\n";
    $csvResponse = $exportService->exportCsv($testData, $headers, 'device_001_history');
    echo "   ✓ CSV导出成功！\n";
    echo "   - Content-Type: " . $csvResponse->getHeader('Content-Type') . "\n";
    echo "   - 数据长度: " . strlen($csvResponse->getContent()) . " bytes\n";
    echo "   - 数据行数: " . (count($testData) + 1) . " 行（含表头）\n\n";
    
    // 2. 测试简单Excel导出
    echo "2. 测试简单Excel导出功能...\n";
    $excelResponse = $exportService->exportExcel($testData, $headers, 'device_001_history');
    echo "   ✓ Excel导出成功！\n";
    echo "   - Content-Type: " . $excelResponse->getHeader('Content-Type') . "\n";
    echo "   - 数据长度: " . strlen($excelResponse->getContent()) . " bytes\n";
    echo "   - 格式: HTML表格（Excel兼容）\n\n";
    
    // 3. 测试智能导出
    echo "3. 测试智能导出功能...\n";
    $smartResponse = $exportService->smartExport($testData, $headers, 'device_001_smart', 'excel');
    echo "   ✓ 智能导出成功！\n";
    echo "   - Content-Type: " . $smartResponse->getHeader('Content-Type') . "\n";
    echo "   - 数据长度: " . strlen($smartResponse->getContent()) . " bytes\n\n";
    
    // 4. 测试设备历史数据表头生成
    echo "4. 测试设备历史数据表头生成...\n";
    $dataTypes = ['flow', 'innerPressure', 'temperature'];
    $deviceHeaders = $exportService->getDeviceHistoryHeaders($dataTypes);
    echo "   ✓ 表头生成成功！\n";
    echo "   - 数据类型: " . implode(', ', $dataTypes) . "\n";
    echo "   - 生成表头: " . implode(', ', $deviceHeaders) . "\n\n";
    
    // 5. 测试不同数据类型组合
    echo "5. 测试不同数据类型组合...\n";
    $combinations = [
        ['flow'],
        ['flow', 'temperature'],
        ['innerPressure', 'outerPressure', 'valveOpening'],
        ['flow', 'innerPressure', 'outerPressure', 'temperature', 'valveOpening']
    ];
    
    foreach ($combinations as $index => $types) {
        $testHeaders = $exportService->getDeviceHistoryHeaders($types);
        $testCsvResponse = $exportService->exportCsv($testData, $testHeaders, "test_combo_$index");
        echo "   ✓ 组合 " . ($index + 1) . ": " . implode(', ', $types) . " - " . strlen($testCsvResponse->getContent()) . " bytes\n";
    }
    echo "\n";
    
    // 6. 测试空数据处理
    echo "6. 测试空数据处理...\n";
    $emptyResponse = $exportService->exportCsv([], $headers, 'empty_test');
    echo "   ✓ 空数据处理成功！\n";
    echo "   - 数据长度: " . strlen($emptyResponse->getContent()) . " bytes（仅包含表头）\n\n";
    
    // 7. 测试大数据量（模拟）
    echo "7. 测试大数据量处理...\n";
    $largeData = [];
    for ($i = 0; $i < 1000; $i++) {
        $largeData[] = [
            'time' => date('Y-m-d H:i:s', strtotime('2024-01-01 00:00:00') + ($i * 60)),
            'flow' => round(rand(1000, 2000) / 10, 1),
            'temperature' => round(rand(400, 600) / 10, 1)
        ];
    }
    $largeHeaders = ['time' => '时间', 'flow' => '流量', 'temperature' => '温度'];
    $largeResponse = $exportService->exportCsv($largeData, $largeHeaders, 'large_data_test');
    echo "   ✓ 大数据量处理成功！\n";
    echo "   - 数据行数: " . count($largeData) . " 行\n";
    echo "   - 数据长度: " . strlen($largeResponse->getContent()) . " bytes\n\n";
    
    echo "=== 所有测试完成！===\n";
    echo "✓ CSV导出功能正常\n";
    echo "✓ Excel导出功能正常（HTML格式，Excel兼容）\n";
    echo "✓ 智能导出功能正常\n";
    echo "✓ 数据验证功能正常\n";
    echo "✓ 大数据量处理正常\n\n";
    
    echo "注意事项：\n";
    echo "- 当前使用的是HTML表格格式的Excel文件，Excel可以正常打开\n";
    echo "- 如需真正的.xlsx格式，请安装PhpSpreadsheet和相关PHP扩展\n";
    echo "- 系统会自动根据环境选择最佳的导出方式\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
