# WSL环境PHP扩展安装指南

## 🐧 WSL环境确认

首先，请确认您在WSL环境中运行：

```bash
# 在WSL终端中运行（不是PowerShell）
uname -a
cat /etc/os-release
```

## 📦 Ubuntu/Debian WSL安装步骤

如果您使用的是Ubuntu或Debian WSL：

### 1. 更新包管理器
```bash
sudo apt update
sudo apt upgrade -y
```

### 2. 安装PHP扩展
```bash
# 安装所有必需的PHP扩展
sudo apt install -y php-mbstring php-gd php-zip php-fileinfo

# 如果上面的命令不工作，尝试具体版本
sudo apt install -y php8.3-mbstring php8.3-gd php8.3-zip php8.3-fileinfo

# 或者安装完整的PHP扩展包
sudo apt install -y php-common php-cli php-fpm php-json php-pdo php-mysql php-zip php-gd php-mbstring php-curl php-xml php-pear php-bcmath
```

### 3. 重启PHP服务（如果使用PHP-FPM）
```bash
sudo systemctl restart php8.3-fpm
# 或者
sudo service php8.3-fpm restart
```

### 4. 验证安装
```bash
php -m | grep -E "(mbstring|gd|zip|fileinfo)"
```

## 📦 CentOS/RHEL WSL安装步骤

如果您使用的是CentOS或RHEL WSL：

### 1. 安装EPEL仓库
```bash
sudo yum install -y epel-release
```

### 2. 安装PHP扩展
```bash
sudo yum install -y php-mbstring php-gd php-zip
```

### 3. 重启服务
```bash
sudo systemctl restart httpd
# 或者
sudo systemctl restart nginx
```

## 🔧 手动编译安装（高级用户）

如果包管理器安装失败，可以手动编译：

```bash
# 安装编译工具
sudo apt install -y build-essential php-dev

# 使用PECL安装（如果需要）
sudo pecl install zip
```

## ✅ 验证安装成功

运行我们的检查脚本：

```bash
# 在项目目录中运行
php setup_php_extensions.php
```

如果显示所有扩展都已安装，然后安装PhpSpreadsheet：

```bash
composer require phpoffice/phpspreadsheet
```

## 🚀 测试导出功能

```bash
# 测试基本功能
php test_export_detailed.php

# 如果PhpSpreadsheet安装成功，会看到真正的Excel导出
# 如果没有，会自动使用HTML格式的Excel兼容文件
```

## 🔍 常见问题解决

### 问题1：权限不足
```bash
# 如果遇到权限问题
sudo chown -R $USER:$USER /path/to/your/project
```

### 问题2：PHP版本不匹配
```bash
# 检查PHP版本
php -v

# 安装对应版本的扩展
sudo apt install php8.3-mbstring  # 替换为您的PHP版本
```

### 问题3：扩展安装后仍然不可用
```bash
# 检查php.ini配置
php --ini

# 手动启用扩展（在php.ini中添加）
extension=mbstring
extension=gd
extension=zip
extension=fileinfo
```

## 📝 WSL特殊注意事项

1. **文件权限**：WSL中的文件权限可能与Windows不同
2. **路径问题**：确保使用Linux路径格式
3. **服务管理**：WSL2中可以使用systemctl，WSL1可能需要service命令

## 🎯 快速解决方案

如果您不想安装扩展，我们的导出功能已经可以工作：

```bash
# 直接测试当前功能
php test_export_detailed.php
```

这会显示：
- ✅ CSV导出正常工作
- ✅ Excel兼容格式正常工作
- ⚠️ 真正的.xlsx格式需要扩展

## 🔄 切换到WSL环境

如果您当前在PowerShell中，请：

1. 打开WSL终端：
   ```powershell
   wsl
   ```

2. 或者直接启动Ubuntu/Debian：
   ```powershell
   ubuntu
   # 或
   debian
   ```

3. 导航到项目目录：
   ```bash
   cd /mnt/g/www/wwwroot/helioCloud-backend
   ```

4. 然后按照上面的Linux安装步骤操作。
