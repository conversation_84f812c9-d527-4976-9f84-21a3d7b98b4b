<?php
/**
 * PHP扩展检查和配置脚本
 * 用于检查和指导安装必要的PHP扩展
 */

echo "=== PHP扩展检查和配置工具 ===\n\n";

// 获取PHP信息
$phpVersion = PHP_VERSION;
$phpIniPath = php_ini_loaded_file();
$phpDir = dirname(PHP_BINARY);

echo "PHP版本: $phpVersion\n";
echo "PHP目录: $phpDir\n";
echo "php.ini路径: $phpIniPath\n\n";

// 检查必需的扩展
$requiredExtensions = [
    'mbstring' => '多字节字符串处理',
    'fileinfo' => '文件信息检测',
    'gd' => '图像处理',
    'zip' => 'ZIP文件处理'
];

echo "=== 扩展状态检查 ===\n";
$missingExtensions = [];
foreach ($requiredExtensions as $ext => $desc) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✓ 已安装' : '✗ 缺失';
    echo sprintf("%-12s: %s (%s)\n", $ext, $status, $desc);
    
    if (!$loaded) {
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    echo "\n🎉 所有必需的扩展都已安装！\n";
    echo "您可以安装PhpSpreadsheet了：\n";
    echo "composer require phpoffice/phpspreadsheet\n";
    exit(0);
}

echo "\n=== 解决方案 ===\n";

// 检查扩展文件是否存在
$extDir = ini_get('extension_dir');
if (empty($extDir)) {
    $extDir = $phpDir . DIRECTORY_SEPARATOR . 'ext';
}

echo "扩展目录: $extDir\n\n";

$solutions = [];

// 检查Windows环境
if (PHP_OS_FAMILY === 'Windows') {
    echo "检测到Windows环境\n\n";
    
    // 检查扩展文件是否存在
    $windowsExtensions = [
        'mbstring' => 'php_mbstring.dll',
        'fileinfo' => 'php_fileinfo.dll', 
        'gd' => 'php_gd.dll',
        'zip' => 'php_zip.dll'
    ];
    
    $iniModifications = [];
    
    foreach ($missingExtensions as $ext) {
        $dllFile = $windowsExtensions[$ext] ?? "php_$ext.dll";
        $dllPath = $extDir . DIRECTORY_SEPARATOR . $dllFile;
        
        if (file_exists($dllPath)) {
            echo "✓ 找到扩展文件: $dllFile\n";
            $iniModifications[] = "extension=$ext";
        } else {
            echo "✗ 扩展文件不存在: $dllPath\n";
            $solutions[] = "下载并安装 $dllFile 到 $extDir";
        }
    }
    
    if (!empty($iniModifications)) {
        echo "\n=== php.ini 修改建议 ===\n";
        echo "请在 php.ini 文件中添加或取消注释以下行：\n\n";
        foreach ($iniModifications as $line) {
            echo "$line\n";
        }
        echo "\n修改后请重启Web服务器。\n";
    }
    
} else {
    // Linux环境
    echo "检测到Linux环境\n\n";
    
    $distro = '';
    if (file_exists('/etc/os-release')) {
        $osRelease = file_get_contents('/etc/os-release');
        if (strpos($osRelease, 'Ubuntu') !== false || strpos($osRelease, 'Debian') !== false) {
            $distro = 'debian';
        } elseif (strpos($osRelease, 'CentOS') !== false || strpos($osRelease, 'Red Hat') !== false) {
            $distro = 'redhat';
        }
    }
    
    if ($distro === 'debian') {
        echo "Ubuntu/Debian 安装命令：\n";
        echo "sudo apt-get update\n";
        echo "sudo apt-get install php-mbstring php-gd php-zip\n";
        echo "sudo systemctl restart apache2  # 或 nginx\n";
    } elseif ($distro === 'redhat') {
        echo "CentOS/RHEL 安装命令：\n";
        echo "sudo yum install php-mbstring php-gd php-zip\n";
        echo "sudo systemctl restart httpd  # 或 nginx\n";
    } else {
        echo "请根据您的Linux发行版安装相应的PHP扩展包\n";
    }
}

// 提供备用解决方案
echo "\n=== 备用解决方案 ===\n";
echo "如果无法安装扩展，您仍然可以使用我们的导出功能：\n";
echo "1. CSV导出 - 完全可用，无需任何扩展\n";
echo "2. 简单Excel导出 - HTML格式，Excel可以打开\n";
echo "3. 运行测试: php test_export_detailed.php\n\n";

// 创建临时的php.ini修改脚本（仅Windows）
if (PHP_OS_FAMILY === 'Windows' && !empty($iniModifications)) {
    $scriptContent = "# 将以下内容添加到 php.ini 文件中\n";
    $scriptContent .= "# 文件位置: $phpIniPath\n\n";
    foreach ($iniModifications as $line) {
        $scriptContent .= "$line\n";
    }
    
    file_put_contents('php_ini_modifications.txt', $scriptContent);
    echo "已生成 php_ini_modifications.txt 文件，包含需要添加到 php.ini 的内容。\n";
}

echo "\n=== 验证步骤 ===\n";
echo "修改完成后，运行以下命令验证：\n";
echo "php setup_php_extensions.php\n";
echo "php test_export_detailed.php\n";
