/**
 * 前端调用设备历史数据导出功能示例
 * 适用于Vue.js、React等前端框架
 */

// 导出设备历史数据的函数
async function exportDeviceHistoryData(params) {
    try {
        // 构建请求参数
        const exportParams = {
            deviceId: params.deviceId,
            startTime: params.startTime,
            endTime: params.endTime,
            dataTypes: params.dataTypes,
            interval: params.interval || '10m',
            format: params.format || 'excel',
            layer: params.layer || null
        };

        // 发送导出请求
        const response = await fetch('/tenant/device/historyDataExport', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + getAccessToken(), // 获取访问令牌
            },
            body: JSON.stringify(exportParams)
        });

        // 检查响应状态
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || '导出失败');
        }

        // 检查响应类型
        const contentType = response.headers.get('Content-Type');
        
        if (contentType && contentType.includes('application/json')) {
            // 如果返回JSON，说明有错误
            const errorData = await response.json();
            throw new Error(errorData.message || '导出失败');
        }

        // 获取文件名
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = 'device_history_export.csv';
        
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename="(.+)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // 创建下载链接
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理URL对象
        window.URL.revokeObjectURL(url);
        
        return {
            success: true,
            message: '导出成功',
            filename: filename
        };

    } catch (error) {
        console.error('导出失败:', error);
        return {
            success: false,
            message: error.message || '导出失败'
        };
    }
}

// Vue.js 组件示例
const DeviceHistoryExport = {
    data() {
        return {
            exportForm: {
                deviceId: '',
                startTime: '',
                endTime: '',
                dataTypes: ['flow', 'innerPressure', 'outerPressure', 'temperature', 'valveOpening'],
                interval: '10m',
                format: 'excel',
                layer: ''
            },
            exporting: false,
            dataTypeOptions: [
                { value: 'flow', label: '流量' },
                { value: 'innerPressure', label: '内压' },
                { value: 'outerPressure', label: '外压' },
                { value: 'temperature', label: '温度' },
                { value: 'valveOpening', label: '阀门开度' }
            ],
            intervalOptions: [
                { value: '1m', label: '1分钟' },
                { value: '5m', label: '5分钟' },
                { value: '10m', label: '10分钟' },
                { value: '30m', label: '30分钟' },
                { value: '1h', label: '1小时' },
                { value: '6h', label: '6小时' },
                { value: '1d', label: '1天' }
            ],
            formatOptions: [
                { value: 'excel', label: 'Excel格式' },
                { value: 'csv', label: 'CSV格式' }
            ]
        };
    },
    methods: {
        async handleExport() {
            // 表单验证
            if (!this.validateForm()) {
                return;
            }

            this.exporting = true;
            
            try {
                const result = await exportDeviceHistoryData(this.exportForm);
                
                if (result.success) {
                    this.$message.success(`导出成功：${result.filename}`);
                } else {
                    this.$message.error(result.message);
                }
            } catch (error) {
                this.$message.error('导出失败：' + error.message);
            } finally {
                this.exporting = false;
            }
        },

        validateForm() {
            if (!this.exportForm.deviceId) {
                this.$message.error('请选择设备');
                return false;
            }
            
            if (!this.exportForm.startTime || !this.exportForm.endTime) {
                this.$message.error('请选择时间范围');
                return false;
            }
            
            if (new Date(this.exportForm.startTime) >= new Date(this.exportForm.endTime)) {
                this.$message.error('开始时间必须小于结束时间');
                return false;
            }
            
            if (!this.exportForm.dataTypes || this.exportForm.dataTypes.length === 0) {
                this.$message.error('请选择至少一种数据类型');
                return false;
            }
            
            return true;
        },

        // 快速设置时间范围
        setTimeRange(type) {
            const now = new Date();
            const end = new Date(now);
            let start = new Date(now);
            
            switch (type) {
                case 'today':
                    start.setHours(0, 0, 0, 0);
                    break;
                case 'yesterday':
                    start.setDate(start.getDate() - 1);
                    start.setHours(0, 0, 0, 0);
                    end.setDate(end.getDate() - 1);
                    end.setHours(23, 59, 59, 999);
                    break;
                case 'week':
                    start.setDate(start.getDate() - 7);
                    break;
                case 'month':
                    start.setMonth(start.getMonth() - 1);
                    break;
            }
            
            this.exportForm.startTime = this.formatDateTime(start);
            this.exportForm.endTime = this.formatDateTime(end);
        },

        formatDateTime(date) {
            return date.toISOString().slice(0, 19).replace('T', ' ');
        }
    },
    
    template: `
        <div class="device-history-export">
            <el-form :model="exportForm" label-width="120px">
                <el-form-item label="设备ID">
                    <el-input v-model="exportForm.deviceId" placeholder="请输入设备ID"></el-input>
                </el-form-item>
                
                <el-form-item label="时间范围">
                    <el-row :gutter="10">
                        <el-col :span="10">
                            <el-date-picker
                                v-model="exportForm.startTime"
                                type="datetime"
                                placeholder="开始时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss">
                            </el-date-picker>
                        </el-col>
                        <el-col :span="10">
                            <el-date-picker
                                v-model="exportForm.endTime"
                                type="datetime"
                                placeholder="结束时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss">
                            </el-date-picker>
                        </el-col>
                        <el-col :span="4">
                            <el-dropdown @command="setTimeRange">
                                <el-button type="text">快速选择</el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="today">今天</el-dropdown-item>
                                    <el-dropdown-item command="yesterday">昨天</el-dropdown-item>
                                    <el-dropdown-item command="week">最近7天</el-dropdown-item>
                                    <el-dropdown-item command="month">最近30天</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </el-col>
                    </el-row>
                </el-form-item>
                
                <el-form-item label="数据类型">
                    <el-checkbox-group v-model="exportForm.dataTypes">
                        <el-checkbox 
                            v-for="option in dataTypeOptions" 
                            :key="option.value" 
                            :label="option.value">
                            {{ option.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                
                <el-form-item label="时间间隔">
                    <el-select v-model="exportForm.interval">
                        <el-option 
                            v-for="option in intervalOptions" 
                            :key="option.value" 
                            :label="option.label" 
                            :value="option.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="导出格式">
                    <el-radio-group v-model="exportForm.format">
                        <el-radio 
                            v-for="option in formatOptions" 
                            :key="option.value" 
                            :label="option.value">
                            {{ option.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                
                <el-form-item label="层级">
                    <el-input v-model="exportForm.layer" placeholder="可选，设备层级信息"></el-input>
                </el-form-item>
                
                <el-form-item>
                    <el-button 
                        type="primary" 
                        @click="handleExport" 
                        :loading="exporting">
                        {{ exporting ? '导出中...' : '导出数据' }}
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
    `
};

// 获取访问令牌的辅助函数（需要根据实际项目调整）
function getAccessToken() {
    // 从localStorage、sessionStorage或其他地方获取token
    return localStorage.getItem('access_token') || '';
}
