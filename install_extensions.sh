#!/bin/bash

# WSL/Linux环境PHP扩展自动安装脚本
# 使用方法: bash install_extensions.sh

echo "=== PHP扩展自动安装脚本 ==="
echo ""

# 检查是否在Linux环境中
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "❌ 此脚本仅适用于Linux/WSL环境"
    echo "请在WSL终端中运行此脚本"
    exit 1
fi

# 检查PHP是否安装
if ! command -v php &> /dev/null; then
    echo "❌ PHP未安装，请先安装PHP"
    exit 1
fi

echo "✅ 检测到PHP版本: $(php -v | head -n1)"
echo ""

# 检测Linux发行版
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "❌ 无法检测Linux发行版"
    exit 1
fi

echo "✅ 检测到系统: $OS $VER"
echo ""

# 获取PHP版本
PHP_VERSION=$(php -r "echo PHP_MAJOR_VERSION.'.'.PHP_MINOR_VERSION;")
echo "✅ PHP版本: $PHP_VERSION"
echo ""

# 检查当前扩展状态
echo "=== 检查当前扩展状态 ==="
extensions=("mbstring" "gd" "zip" "fileinfo")
missing_extensions=()

for ext in "${extensions[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo "✅ $ext: 已安装"
    else
        echo "❌ $ext: 未安装"
        missing_extensions+=("$ext")
    fi
done

if [ ${#missing_extensions[@]} -eq 0 ]; then
    echo ""
    echo "🎉 所有扩展都已安装！"
    echo "您可以安装PhpSpreadsheet了:"
    echo "composer require phpoffice/phpspreadsheet"
    exit 0
fi

echo ""
echo "=== 开始安装缺失的扩展 ==="

# 根据发行版安装扩展
if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
    echo "📦 使用apt包管理器安装..."
    
    # 更新包列表
    echo "更新包列表..."
    sudo apt update
    
    # 安装扩展
    for ext in "${missing_extensions[@]}"; do
        echo "安装 php-$ext..."
        sudo apt install -y "php$PHP_VERSION-$ext" || sudo apt install -y "php-$ext"
    done
    
elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]] || [[ $OS == *"Fedora"* ]]; then
    echo "📦 使用yum/dnf包管理器安装..."
    
    # 选择包管理器
    if command -v dnf &> /dev/null; then
        PKG_MGR="dnf"
    else
        PKG_MGR="yum"
    fi
    
    # 安装扩展
    for ext in "${missing_extensions[@]}"; do
        echo "安装 php-$ext..."
        sudo $PKG_MGR install -y "php-$ext"
    done
    
else
    echo "❌ 不支持的Linux发行版: $OS"
    echo "请手动安装以下PHP扩展:"
    for ext in "${missing_extensions[@]}"; do
        echo "  - php-$ext"
    done
    exit 1
fi

echo ""
echo "=== 验证安装结果 ==="

# 重新检查扩展
all_installed=true
for ext in "${extensions[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo "✅ $ext: 安装成功"
    else
        echo "❌ $ext: 安装失败"
        all_installed=false
    fi
done

echo ""
if [ "$all_installed" = true ]; then
    echo "🎉 所有扩展安装成功！"
    echo ""
    echo "下一步操作:"
    echo "1. 安装PhpSpreadsheet:"
    echo "   composer require phpoffice/phpspreadsheet"
    echo ""
    echo "2. 测试导出功能:"
    echo "   php test_export_detailed.php"
else
    echo "⚠️  部分扩展安装失败"
    echo "但您仍然可以使用CSV和简单Excel导出功能"
    echo ""
    echo "测试当前功能:"
    echo "php test_export_detailed.php"
fi

echo ""
echo "=== 安装完成 ==="
