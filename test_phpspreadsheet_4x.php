<?php
/**
 * PhpSpreadsheet 4.x 兼容性测试脚本
 * 测试新的API方法是否正常工作
 */

require_once 'vendor/autoload.php';

use app\common\service\ExportService;

echo "=== PhpSpreadsheet 4.x 兼容性测试 ===\n\n";

// 检查PhpSpreadsheet版本
if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    $reflection = new ReflectionClass('\PhpOffice\PhpSpreadsheet\Spreadsheet');
    $filename = $reflection->getFileName();
    
    // 尝试从composer.lock获取版本信息
    $composerLock = 'vendor/composer/installed.json';
    if (file_exists($composerLock)) {
        $installed = json_decode(file_get_contents($composerLock), true);
        $packages = $installed['packages'] ?? $installed;
        
        foreach ($packages as $package) {
            if ($package['name'] === 'phpoffice/phpspreadsheet') {
                echo "✅ PhpSpreadsheet版本: " . $package['version'] . "\n";
                break;
            }
        }
    }
    
    echo "✅ PhpSpreadsheet已安装\n";
    echo "📁 安装路径: " . dirname($filename) . "\n\n";
} else {
    echo "❌ PhpSpreadsheet未安装\n";
    exit(1);
}

// 测试新的API方法
echo "=== 测试新API方法 ===\n";

try {
    // 测试Coordinate::stringFromColumnIndex
    $coordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(1);
    echo "✅ Coordinate::stringFromColumnIndex(1) = $coordinate\n";
    
    $coordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(26);
    echo "✅ Coordinate::stringFromColumnIndex(26) = $coordinate\n";
    
    // 测试创建Spreadsheet
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    echo "✅ Spreadsheet创建成功\n";
    
    // 测试setCellValue方法
    $sheet->setCellValue('A1', '测试标题');
    echo "✅ setCellValue方法正常\n";
    
    // 测试样式设置
    $sheet->getStyle('A1')->getFont()->setBold(true);
    echo "✅ 样式设置正常\n";
    
    // 测试列宽设置
    $sheet->getColumnDimension('A')->setAutoSize(true);
    echo "✅ 列宽设置正常\n";
    
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ API测试失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试导出服务
echo "=== 测试导出服务 ===\n";

$testData = [
    [
        'time' => '2024-01-01 10:00:00',
        'flow' => 150.5,
        'temperature' => 45.2
    ],
    [
        'time' => '2024-01-01 10:10:00',
        'flow' => 148.3,
        'temperature' => 45.8
    ]
];

$headers = [
    'time' => '时间',
    'flow' => '流量 (L/min)',
    'temperature' => '温度 (°C)'
];

try {
    $exportService = new ExportService();
    
    // 测试Excel导出
    echo "测试Excel导出...\n";
    $excelResponse = $exportService->exportExcel($testData, $headers, 'test_4x_compatibility');
    echo "✅ Excel导出成功！\n";
    echo "   - Content-Type: " . $excelResponse->getHeader('Content-Type') . "\n";
    echo "   - 数据长度: " . strlen($excelResponse->getContent()) . " bytes\n";
    
    // 检查是否使用了真正的Excel格式
    $contentType = $excelResponse->getHeader('Content-Type');
    if (strpos($contentType, 'openxmlformats-officedocument.spreadsheetml.sheet') !== false) {
        echo "✅ 使用真正的.xlsx格式\n";
    } else {
        echo "ℹ️  使用HTML兼容格式\n";
    }
    
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ 导出服务测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
    exit(1);
}

// 测试智能导出
echo "=== 测试智能导出 ===\n";

try {
    $smartResponse = $exportService->smartExport($testData, $headers, 'test_smart_4x', 'excel');
    echo "✅ 智能导出成功！\n";
    echo "   - Content-Type: " . $smartResponse->getHeader('Content-Type') . "\n";
    echo "   - 数据长度: " . strlen($smartResponse->getContent()) . " bytes\n";
    
} catch (Exception $e) {
    echo "❌ 智能导出测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 兼容性测试完成 ===\n";
echo "🎉 PhpSpreadsheet 4.x 兼容性测试通过！\n";
echo "\n";

echo "=== 使用建议 ===\n";
echo "1. 您的PhpSpreadsheet 4.x版本已经完全兼容\n";
echo "2. 可以正常使用真正的.xlsx格式导出\n";
echo "3. 所有API方法都已更新到最新版本\n";
echo "4. 建议运行完整测试: php test_export_detailed.php\n";
