# Redis连接池监控使用说明

## 概述

Redis连接池监控系统提供了全面的Redis连接池性能监控和管理功能，包括实时统计、健康检查、性能分析等。

## 功能特性

### 1. 自动监控
- **连接池状态监控**：实时监控连接池大小、可用连接数、等待连接数等
- **操作统计**：记录总操作数、成功/失败次数、成功率等
- **性能指标**：平均执行时间、总执行时间等
- **连接统计**：连接获取/归还次数、连接失败次数等

### 2. 监控报告
- **定期报告**：每5分钟自动输出详细监控报告
- **实时查询**：通过API随时获取当前状态
- **健康检查**：自动评估系统健康状态并给出建议

### 3. Web界面
- **实时监控面板**：通过Web界面查看实时数据
- **图表展示**：可视化展示性能趋势
- **操作控制**：启用/禁用监控、重置统计等

## 使用方法

### 1. 命令行测试

#### 运行Redis监控测试
```bash
php think tenant:redisMonitorTest
```

这个命令会执行以下测试：
- 基本操作测试（设置/获取/删除）
- 并发操作测试（20个协程并发执行）
- 事务操作测试
- 管道操作测试
- 错误处理测试

#### 查看设备状态监控服务
```bash
# 启用连接池监控（默认）
php think tenant:deviceStatusMonitor
```

设备状态监控服务会每5秒自动输出Redis连接池监控报告（如果启用了监控）。

### 2. Web API接口

#### 获取连接池统计信息
```http
GET /tenant/redisMonitor/stats
```

返回示例：
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "pool_stats": {
            "pool_size": 50,
            "current_count": 10,
            "available": 8,
            "waiting": 0,
            "connection_times": 1250,
            "active_connections": 2
        },
        "operation_stats": {
            "total_operations": 1000,
            "successful_operations": 995,
            "failed_operations": 5,
            "total_execution_time": 2.5,
            "avg_execution_time": 0.0025,
            "connection_gets": 1000,
            "connection_puts": 998,
            "connection_failures": 2
        },
        "performance": {
            "success_rate": 99.5,
            "avg_execution_time_ms": 2.5
        },
        "monitoring_enabled": true,
        "timestamp": **********
    }
}
```

#### 获取监控报告
```http
GET /tenant/redisMonitor/report
```

#### 重置统计信息
```http
POST /tenant/redisMonitor/reset
```

#### 启用/禁用监控
```http
POST /tenant/redisMonitor/toggle
Content-Type: application/json

{
    "enable": true
}
```

#### 健康检查
```http
GET /tenant/redisMonitor/health
```

返回示例：
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "status": "healthy",
        "score": 95,
        "issues": [
            "连接池使用率较高 (85%)"
        ]
    }
}
```

#### 连接测试
```http
GET /tenant/redisMonitor/test
```

### 3. 编程接口

#### 基本使用

```php
use app\common\service\redis\RedisPoolService;

$redisService = RedisPoolService::getInstance();

// 获取统计信息
$stats = $redisService->getStats();

// 获取监控报告
$report = $redisService->getMonitoringReport();

// 重置统计
$redisService->resetStats();

// 启用/禁用监控
$redisService->setMonitoring(true);
```

#### 在业务代码中使用
```php
// 简单操作
$redisService->set('key', 'value', 3600);
$value = $redisService->get('key');

// 复杂操作
$result = $redisService->execute(function($redis) {
    $redis->hSet('hash_key', 'field', 'value');
    return $redis->hGetAll('hash_key');
});

// 事务操作
$results = $redisService->transaction(function($redis) {
    $redis->set('key1', 'value1');
    $redis->set('key2', 'value2');
    $redis->incr('counter');
});

// 管道操作
$results = $redisService->pipeline(function($pipeline) {
    $pipeline->set('key1', 'value1');
    $pipeline->set('key2', 'value2');
    $pipeline->get('key1');
    $pipeline->get('key2');
});
```

## 监控指标说明

### 连接池指标
- **pool_size**：连接池总大小
- **current_count**：当前连接数
- **available**：可用连接数
- **waiting**：等待连接的协程数
- **active_connections**：活跃连接数

### 操作指标
- **total_operations**：总操作数
- **successful_operations**：成功操作数
- **failed_operations**：失败操作数
- **success_rate**：成功率（百分比）
- **avg_execution_time_ms**：平均执行时间（毫秒）

### 连接指标
- **connection_gets**：连接获取次数
- **connection_puts**：连接归还次数
- **connection_failures**：连接失败次数

## 性能优化建议

### 1. 连接池大小调优
- 根据并发量调整连接池大小
- 监控连接池使用率，避免过高或过低
- 建议使用率保持在60-80%之间

### 2. 监控告警
- 成功率低于95%时需要关注
- 平均执行时间超过50ms需要优化
- 连接失败率超过2%需要检查网络和Redis服务

### 3. 资源管理
- 定期重置统计信息避免数据累积
- 在生产环境中可以禁用详细监控以提高性能
- 合理设置监控报告间隔

## 故障排查

### 1. 连接池耗尽
- 检查是否有连接泄漏
- 增加连接池大小
- 优化业务逻辑减少连接占用时间

### 2. 操作失败率高
- 检查Redis服务状态
- 检查网络连接
- 查看详细错误日志

### 3. 性能问题
- 分析慢查询
- 优化Redis操作
- 检查网络延迟

## 注意事项

1. **监控开销**：启用监控会有轻微的性能开销，生产环境中可根据需要调整
2. **内存使用**：统计数据会占用一定内存，建议定期重置
3. **协程环境**：监控功能在协程环境中效果最佳
4. **日志输出**：监控报告会输出到日志，注意日志文件大小
