# Redis管理器使用示例

## 概述

`RedisManager` 是一个简化Redis操作的管理器，自动处理连接池的获取和释放，让开发者专注于业务逻辑而不是连接管理。

## 主要优势

1. **自动连接管理**：无需手动获取和释放连接
2. **简洁的API**：提供便捷方法和高级操作
3. **协程安全**：在Swoole协程环境中自动使用连接池
4. **错误处理**：统一的异常处理机制
5. **事务支持**：简化Redis事务操作
6. **管道支持**：简化Redis管道操作

## 基本使用

### 获取实例

```php
use app\common\service\RedisManager;

$redisManager = RedisManager::getInstance();
```

### 基本操作

```php
// 设置键值
$redisManager->set('key', 'value', 3600); // 带过期时间

// 获取键值
$value = $redisManager->get('key');

// 删除键
$redisManager->del('key');

// 检查键是否存在
$exists = $redisManager->exists('key');

// 设置过期时间
$redisManager->expire('key', 3600);
```

### 哈希表操作

```php
// 设置哈希字段
$redisManager->hSet('hash_key', 'field', 'value');

// 获取哈希字段
$value = $redisManager->hGet('hash_key', 'field');

// 获取所有哈希字段
$allFields = $redisManager->hGetAll('hash_key');
```

### 集合操作

```php
// 添加集合成员
$redisManager->sAdd('set_key', 'member1', 'member2');

// 获取所有集合成员
$members = $redisManager->sMembers('set_key');

// 移除集合成员
$redisManager->sRem('set_key', 'member1');
```

## 高级操作

### 自定义操作

```php
// 执行自定义Redis操作
$result = $redisManager->execute(function($redis) {
    // 在这里执行任何Redis操作
    $redis->hMset('device:status', [
        'status' => 'online',
        'timestamp' => time()
    ]);
    
    return $redis->hGetAll('device:status');
});
```

### 事务操作

```php
// 执行Redis事务
$results = $redisManager->transaction(function($redis) {
    $redis->set('key1', 'value1');
    $redis->set('key2', 'value2');
    $redis->incr('counter');
});

// 检查事务结果
if ($results && !in_array(false, $results, true)) {
    echo "事务执行成功";
} else {
    echo "事务执行失败";
}
```

### 管道操作

```php
// 执行Redis管道
$results = $redisManager->pipeline(function($pipeline) {
    $pipeline->set('key1', 'value1');
    $pipeline->set('key2', 'value2');
    $pipeline->get('key1');
    $pipeline->get('key2');
});

// 处理管道结果
foreach ($results as $result) {
    echo "结果: " . $result . "\n";
}
```

## 对比：改进前后

### 改进前（繁琐的连接管理）

```php
protected function updateDeviceStatus(string $tenantCode, int $deviceId, string $status): void
{
    $redis = null;
    try {
        // 手动获取连接
        $redis = $this->getRedis();
        
        // 业务逻辑
        $redis->hSet("device:{$deviceId}", 'status', $status);
        $redis->expire("device:{$deviceId}", 3600);
        
    } catch (\Exception $e) {
        $this->logError('操作失败: ' . $e->getMessage());
    } finally {
        // 手动释放连接
        if ($redis && $this->redisPool) {
            try {
                $this->redisPool->put($redis);
            } catch (\Throwable $e) {
                Log::debug('归还连接异常: ' . $e->getMessage());
            }
        }
    }
}
```

### 改进后（简洁的操作）

```php
protected function updateDeviceStatus(string $tenantCode, int $deviceId, string $status): void
{
    try {
        // 直接执行业务逻辑，连接管理自动处理
        $this->redisManager->execute(function($redis) use ($deviceId, $status) {
            $redis->hSet("device:{$deviceId}", 'status', $status);
            $redis->expire("device:{$deviceId}", 3600);
        });
    } catch (\Exception $e) {
        $this->logError('操作失败: ' . $e->getMessage());
    }
}
```

## 错误处理

```php
try {
    $result = $redisManager->execute(function($redis) {
        // 可能失败的操作
        return $redis->get('non_existent_key');
    });
} catch (\Exception $e) {
    // 统一的错误处理
    Log::error('Redis操作失败: ' . $e->getMessage());
}
```

## 连接池状态监控

```php
// 获取连接池状态
$stats = $redisManager->getStats();
echo "连接池大小: " . $stats['pool_size'] . "\n";
echo "当前连接数: " . $stats['current_count'] . "\n";
echo "可用连接数: " . $stats['available'] . "\n";
echo "等待连接数: " . $stats['waiting'] . "\n";
```

## 最佳实践

1. **使用单例模式**：通过 `getInstance()` 获取实例
2. **优先使用便捷方法**：对于常见操作，使用提供的便捷方法
3. **复杂操作使用execute**：对于复杂的Redis操作，使用 `execute()` 方法
4. **事务操作使用transaction**：确保原子性的操作使用 `transaction()` 方法
5. **批量操作使用pipeline**：提高性能的批量操作使用 `pipeline()` 方法
6. **适当的错误处理**：在关键操作周围添加try-catch块

## 注意事项

1. 在非协程环境中，会自动使用ThinkPHP的Redis连接
2. 在协程环境中，会自动使用连接池管理连接
3. 连接池的大小会根据CPU核心数自动调整
4. 所有操作都有统一的超时处理机制
