<?php
declare(strict_types = 1);

namespace app\common\service;

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\Response;

/**
 * 数据导出服务类
 * 支持CSV和Excel格式导出
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class ExportService
{
    /**
     * 导出CSV格式数据
     * @param array $data 要导出的数据
     * @param array $headers 表头配置 ['field' => 'display_name']
     * @param string $filename 文件名（不含扩展名）
     * @return Response
     */
    public function exportCsv(array $data, array $headers, string $filename = 'export'): Response
    {
        // 设置文件名
        $filename = $filename . '_' . date('YmdHis') . '.csv';

        // 创建CSV内容
        $csvContent = $this->generateCsvContent($data, $headers);

        // 返回文件下载响应
        return Response::create($csvContent, 'html', 200, [
            'Content-Type'        => 'application/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control'       => 'max-age=0',
        ]);
    }

    /**
     * 导出Excel格式数据
     * @param array $data 要导出的数据
     * @param array $headers 表头配置 ['field' => 'display_name']
     * @param string $filename 文件名（不含扩展名）
     * @throws \Exception
     * @return Response
     */
    public function exportExcel(array $data, array $headers, string $filename = 'export'): Response
    {
        // 优先使用PhpSpreadsheet（如果可用）
        if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            $requiredExtensions = ['fileinfo', 'gd', 'mbstring', 'zip'];
            $missingExtensions  = [];

            foreach ($requiredExtensions as $ext) {
                if (!extension_loaded($ext)) {
                    $missingExtensions[] = $ext;
                }
            }

            if (empty($missingExtensions)) {
                return $this->generateExcelFile($data, $headers, $filename);
            }
        }

        // 降级到简单的Excel格式（实际上是HTML表格，但Excel可以打开）
        return $this->generateSimpleExcel($data, $headers, $filename);
    }

    /**
     * 智能导出 - 根据环境自动选择CSV或Excel
     * @param array $data 要导出的数据
     * @param array $headers 表头配置 ['field' => 'display_name']
     * @param string $filename 文件名（不含扩展名）
     * @param string $preferredFormat 首选格式 'excel' 或 'csv'
     * @return Response
     */
    public function smartExport(array $data, array $headers, string $filename = 'export', string $preferredFormat = 'excel'): Response
    {
        try {
            if ($preferredFormat === 'excel') {
                return $this->exportExcel($data, $headers, $filename);
            }
        } catch (\Exception $e) {
            // 如果Excel导出失败，降级到CSV
            error_log('Excel export failed, falling back to CSV: ' . $e->getMessage());
        }

        return $this->exportCsv($data, $headers, $filename);
    }

    /**
     * 生成CSV内容
     * @param array $data 数据
     * @param array $headers 表头配置
     * @return string
     */
    private function generateCsvContent(array $data, array $headers): string
    {
        $output = fopen('php://temp', 'r+');

        // 添加BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");

        // 写入表头
        fputcsv($output, array_values($headers));

        // 写入数据
        foreach ($data as $row) {
            $csvRow = [];

            foreach (array_keys($headers) as $field) {
                $csvRow[] = $row[$field] ?? '';
            }
            fputcsv($output, $csvRow);
        }

        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);

        return $csvContent;
    }

    /**
     * 生成简单的Excel文件（HTML表格格式，Excel可以打开）
     * @param array $data 数据
     * @param array $headers 表头配置
     * @param string $filename 文件名
     * @return Response
     */
    private function generateSimpleExcel(array $data, array $headers, string $filename): Response
    {
        // 生成HTML表格内容
        $html = $this->generateHtmlTable($data, $headers);

        // 设置文件名
        $filename = $filename . '_' . date('YmdHis') . '.xls';

        return Response::create($html, 'html', 200, [
            'Content-Type'        => 'application/vnd.ms-excel; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control'       => 'max-age=0',
        ]);
    }

    /**
     * 生成HTML表格
     * @param array $data 数据
     * @param array $headers 表头配置
     * @return string
     */
    private function generateHtmlTable(array $data, array $headers): string
    {
        $html = '<!DOCTYPE html>';
        $html .= '<html><head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<style>';
        $html .= 'table { border-collapse: collapse; width: 100%; }';
        $html .= 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
        $html .= 'th { background-color: #f2f2f2; font-weight: bold; }';
        $html .= 'tr:nth-child(even) { background-color: #f9f9f9; }';
        $html .= '</style>';
        $html .= '</head><body>';
        $html .= '<table>';

        // 表头
        $html .= '<thead><tr>';

        foreach ($headers as $header) {
            $html .= '<th>' . htmlspecialchars($header, ENT_QUOTES, 'UTF-8') . '</th>';
        }
        $html .= '</tr></thead>';

        // 数据行
        $html .= '<tbody>';

        foreach ($data as $row) {
            $html .= '<tr>';

            foreach (array_keys($headers) as $field) {
                $value = $row[$field] ?? '';
                $html .= '<td>' . htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8') . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';

        $html .= '</table>';
        $html .= '</body></html>';

        return $html;
    }

    /**
     * 生成Excel文件（使用PhpSpreadsheet）
     * @param array $data 数据
     * @param array $headers 表头配置
     * @param string $filename 文件名
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     * @return Response
     */
    private function generateExcelFile(array $data, array $headers, string $filename): Response
    {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();

        // 设置表头 - 使用新的API方法兼容PhpSpreadsheet 4.x
        $col = 1;

        foreach ($headers as $header) {
            $cellCoordinate = Coordinate::stringFromColumnIndex($col) . '1';
            $sheet->setCellValue($cellCoordinate, $header);
            $col++;
        }

        // 设置表头样式
        $headerRange = 'A1:' . Coordinate::stringFromColumnIndex(count($headers)) . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFE0E0E0');

        // 填充数据 - 使用新的API方法兼容PhpSpreadsheet 4.x
        $row = 2;

        foreach ($data as $dataRow) {
            $col = 1;

            foreach (array_keys($headers) as $field) {
                $cellCoordinate = Coordinate::stringFromColumnIndex($col) . $row;
                $sheet->setCellValue($cellCoordinate, $dataRow[$field] ?? '');
                $col++;
            }
            $row++;
        }

        // 自动调整列宽 - 使用新的API方法兼容PhpSpreadsheet 4.x
        foreach (range(1, count($headers)) as $col) {
            $columnLetter = Coordinate::stringFromColumnIndex($col);
            $sheet->getColumnDimension($columnLetter)->setAutoSize(true);
        }

        // 生成文件
        $writer = new Xlsx($spreadsheet);

        // 输出到缓冲区
        ob_start();
        $writer->save('php://output');
        $excelContent = ob_get_clean();

        // 设置文件名
        $filename = $filename . '_' . date('YmdHis') . '.xlsx';

        return Response::create($excelContent, 'html', 200, [
            'Content-Type'        => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control'       => 'max-age=0',
        ]);
    }

    /**
     * 格式化设备历史数据的表头
     * @param array $dataTypes 数据类型数组
     * @return array
     */
    public function getDeviceHistoryHeaders(array $dataTypes): array
    {
        $headers = [
            'time' => '时间',
        ];

        // 如果有layer字段，添加到表头
        if (isset($dataTypes['layer'])) {
            $headers['layer'] = '层级';
        }

        // 数据类型映射
        $typeMapping = [
            'flow'          => '流量 (L/min)',
            'innerPressure' => '内压 (MPa)',
            'outerPressure' => '外压 (MPa)',
            'temperature'   => '温度 (°C)',
            'valveOpening'  => '阀门开度 (%)',
        ];

        foreach ($dataTypes as $type) {
            if (isset($typeMapping[$type])) {
                $headers[$type] = $typeMapping[$type];
            }
        }

        return $headers;
    }
}
