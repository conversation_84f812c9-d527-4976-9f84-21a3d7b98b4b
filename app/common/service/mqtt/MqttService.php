<?php
declare(strict_types = 1);

namespace app\common\service\mqtt;

use app\common\service\CommonService;
use Simps\MQTT\Client;
use Simps\MQTT\Config\ClientConfig;
use Simps\MQTT\Hex\ReasonCode;
use Simps\MQTT\Protocol\V3;
use Simps\MQTT\Protocol\V5;
use think\facade\Config;
use think\facade\Log;

/**
 * MQTT服务类
 * 用于处理MQTT连接、发布和订阅等功能
 * 支持MQTT v5协议，同时兼容v3
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service
 */
class MqttService extends CommonService
{
    /**
     * 单例实例
     * @var ?MqttService
     */
    private static ?MqttService $instance = null;

    /**
     * MQTT客户端
     * @var ?Client
     */
    private ?Client $client = null;

    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 协议版本，默认使用v5
     * @var int
     */
    private int $protocolVersion;

    /**
     * 是否已连接
     * @var bool
     */
    private bool $connected = false;

    /**
     * 连接配置
     * @var ?ClientConfig
     */
    private ?ClientConfig $clientConfig = null;

    /**
     * 私有构造函数，防止外部实例化
     */
    private function __construct()
    {
        parent::__construct();

        $this->config = Config::get('mqtt');

        // 协议版本，默认使用v5
        $this->protocolVersion = intval($this->config['protocol_version'] ?? 5);
    }

    /**
     * 获取单例实例
     * @return MqttService
     */
    public static function getInstance(): self
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 获取连接配置
     * @return \Simps\MQTT\Config\ClientConfig
     */
    protected function getConnectConfig(): ClientConfig
    {
        if ($this->clientConfig !== null) {
            return $this->clientConfig;
        }

        $this->clientConfig = new ClientConfig();

        // 生成客户端ID
        $clientId = $this->config['client_id_prefix'] . Client::genClientID();

        $this->clientConfig
            ->setClientId($clientId)
            ->setKeepAlive((int)$this->config['keepalive'])
            ->setDelay(3000) // 重连时的延迟时间 (毫秒)
            ->setMaxAttempts(-1); // 最大重连次数。默认-1，表示不限制

        // 设置基本认证信息
        // 如果配置了服务器专用认证信息，优先使用服务器认证
        if ($this->config['server_username'] && $this->config['server_password']) {
            $this->clientConfig
                ->setUserName($this->config['server_username'])
                ->setPassword($this->config['server_password']);
        } elseif ($this->config['username'] && $this->config['password']) {
            $this->clientConfig
                ->setUserName($this->config['username'])
                ->setPassword($this->config['password']);
        }

        if ($this->protocolVersion == 5) {
            // MQTT v5 特有配置
            $this->clientConfig->setProtocolLevel(5);
            $this->clientConfig->setProperties([ // MQTT5 中所需要的属性
                'session_expiry_interval' => 0, // 会话过期间隔，0表示永不过期
                'receive_maximum'         => 100, // 接收最大值
                'topic_alias_maximum'     => 10, // 主题别名最大值
            ]);
        } else {
            // MQTT v3 配置
            $this->clientConfig->setProtocolLevel(4); // MQTT 3.1.1
        }

        $swooleConfig = [
            'open_mqtt_protocol' => true,
            'package_max_length' => 2 * 1024 * 1024,
            'connect_timeout'    => (float)$this->config['connect_timeout'],
            'write_timeout'      => (float)$this->config['write_timeout'],
            'read_timeout'       => (float)$this->config['read_timeout'],
        ];

        $this->clientConfig->setSwooleConfig($swooleConfig);

        return $this->clientConfig;
    }

    /**
     * MQTT5中新增的认证交换机制
     * @param int $code 响应码，MQTT5 中需要，MQTT3直接调用即可
     * @param array $properties 属性，MQTT5 中需要
     * @return bool
     */
    public function auth(int $code = ReasonCode::SUCCESS, array $properties = []): bool
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        return $this->client->auth($code, $properties);
    }

    /**
     * 连接MQTT服务器
     * @param array $willMessage 遗嘱消息，可根据需要配置
     * @return bool 是否连接成功
     */
    public function connect(array $willMessage = []): bool
    {
        if ($this->connected && $this->client) {
            return true;
        }

        try {
            // 创建客户端实例，在协程环境中使用
            $this->client = new Client($this->config['host'], (int)$this->config['port'], $this->getConnectConfig());

            // 连接服务器
            $result = $this->client->connect((bool)$this->config['clean_session'], $willMessage);

            // 检查连接结果
            $this->connected = $result['code'] === ReasonCode::SUCCESS;

            if ($this->connected) {
                Log::info("MQTT连接成功: {$this->config['host']}:{$this->config['port']},协议版本：v{$this->protocolVersion}");
            } else {
                $errorMessage = "MQTT连接失败: {$this->config['host']}:{$this->config['port']},协议版本：v{$this->protocolVersion}";
                $errorMessage .= "\n用户名: {$this->config['username']}";
                $errorMessage .= "\n错误代码: {$result['code']}";

                if (isset($result['properties'])) {
                    $errorMessage .= "\n错误属性: " . json_encode($result['properties'], JSON_UNESCAPED_UNICODE);
                }
                Log::error($errorMessage);
            }

            return $this->connected;
        } catch (\Exception $e) {
            Log::error('MQTT连接异常: ' . $e->getMessage());
            $this->connected = false;

            return false;
        }
    }

    /**
     * 重新连接MQTT服务器
     * @return bool 是否重连成功
     */
    public function reconnect(): bool
    {
        $this->connected = false;
        $this->client    = null;

        // 等待重连延迟时间
        sleep((int)$this->config['reconnect_delay']);

        return $this->connect();
    }

    /**
     * 发布消息到指定主题
     * @param string $topic 主题
     * @param string $message 消息内容
     * @param int|null $qos QoS级别
     * @param int|null $dup 重发标志，默认 0
     * @param int|null $retain 是否保留消息
     * @param array $properties 属性，MQTT5 中需要，可选
     * @return bool 是否发布成功
     */
    public function publish(string $topic, string $message, ?int $qos = 0, ?int $dup = 0, ?int $retain = 0, array $properties = []): bool
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            $qos    ??= (int)$this->config['qos'];
            $retain ??= (int)$this->config['retain'];

            if ($this->protocolVersion == 5) {
                // MQTT v5 发布
                /*$properties = $properties ?? [
                    'content_type'            => 'application/json', // 内容类型
                    'message_expiry_interval' => 3600, // 消息过期间隔，单位秒
                ];*/

                $result = $this->client->publish($topic, $message, $qos, $dup, $retain, $properties);

                if (is_bool($result)) {
                    return $result;
                }

                return $result['reason_code'] === ReasonCode::SUCCESS;
            }

            // MQTT v3 发布
            return $this->client->publish($topic, $message, $qos, $dup, $retain);
        } catch (\Exception $e) {
            Log::error('MQTT发布消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 订阅主题
     * @param array $topics 主题数组
     * @param array $properties
     * @return bool 是否订阅成功
     */
    public function subscribe(array $topics, array $properties = []): bool
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            if ($this->protocolVersion == 5) {
                // MQTT v5 订阅
                $result = $this->client->subscribe($topics, $properties);
            } else {
                // 使用v3协议时，将v5的topics转换成v3格式
                $topics = $this->transferTopics2V3($topics);
                // MQTT v3 订阅
                $result = $this->client->subscribe($topics);
            }

            // 检查每个主题的订阅结果
            foreach ($result['codes'] as $code) {
                if ($code > 2) { // 0,1,2 都是成功的状态码
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('MQTT订阅主题异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 取消订阅主题
     * @param array $topics 主题数组
     * @param array $properties
     * @return bool 是否取消成功
     */
    public function unsubscribe(array $topics, array $properties = []): bool
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            if ($this->protocolVersion == 5) {
                // MQTT v5 取消订阅，将主题字符串作为数组传递
                $result = $this->client->unSubscribe($topics, $properties);

                // 检查每个主题的取消订阅结果
                foreach ($result['codes'] as $code) {
                    if ($code !== ReasonCode::SUCCESS) {
                        return false;
                    }
                }
            } else {
                // MQTT v3 取消订阅
                $result = $this->client->unSubscribe($topics);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('MQTT取消订阅主题异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 接收消息
     * @return array|bool 接收到的消息或失败
     */
    public function receive(): bool|array
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            $message = $this->client->recv();

            if ($message) {
                return $message;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('MQTT接收消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 发送消息
     * @param array $data 要发送的数据，必须包含type等信息
     * @param bool $response 是否需要回执。如果为true，会调用一次recv()
     * @return array|bool 发送结果
     */
    public function send(array $data, bool $response = true): bool|array
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            $result = $this->client->send($data, $response);

            if ($result) {
                return $result;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('MQTT发送消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 发送心跳包
     * @return bool 是否发送成功
     */
    public function ping(): bool
    {
        if (!$this->connected && !$this->connect()) {
            return false;
        }

        try {
            Log::error('MQTT发送心跳 ');

            return $this->client->ping();
        } catch (\Exception $e) {
            Log::error('MQTT心跳异常: ' . $e->getMessage());
            $this->connected = false;

            return false;
        }
    }

    /**
     * 构建MQTT主题
     * @param string $pattern 主题模式，例如：{prefix}/tenant/{tenant_code}/heartbeat
     * @param array $params 替换参数，例如：['tenant_code' => 'abcde']
     * @return string 完整的主题名称
     */
    public function buildTopic(string $pattern, array $params = []): string
    {
        $topic = $pattern;

        // 替换主题前缀
        if (str_contains($topic, '{prefix}')) {
            $topic = str_replace('{prefix}', $this->config['topic_prefix'], $topic);
        }

        // 替换其他参数
        foreach ($params as $key => $value) {
            $topic = str_replace('{' . $key . '}', $value, $topic);
        }

        return $topic;
    }

    /**
     * 断开连接
     * @param int $resCode 响应码
     * @param array $properties 属性，MQTT5 中需要
     * @return bool 是否断开成功
     */
    public function disconnect(int $resCode = ReasonCode::NORMAL_DISCONNECTION, array $properties = []): bool
    {
        if (!$this->connected || !$this->client) {
            return true;
        }

        try {
            if ($this->protocolVersion == 5) {
                // MQTT v5 断开连接
                $this->client->close($resCode, $properties);
            } else {
                // MQTT v3 断开连接
                $this->client->close();
            }

            $this->connected = false;
            $this->client    = null;

            return true;
        } catch (\Exception $e) {
            Log::error('MQTT断开连接异常: ' . $e->getMessage());
            $this->connected = false;
            $this->client    = null;

            return false;
        }
    }

    /**
     * 清理MQTT保留消息
     * 通过发送空消息到指定主题来删除保留消息
     * @param string $topic 要清理的主题
     * @return bool 是否清理成功
     */
    public function clearRetainedMessage(string $topic): bool
    {
        if (!$this->connected && !$this->connect()) {
            Log::error("清理保留消息失败: MQTT未连接，主题[{$topic}]");
            return false;
        }

        try {
            // 发送空消息并设置retain=1来清理保留消息
            // 参数顺序：topic, message, qos, dup, retain
            $result = $this->publish($topic, '', 0, 0, 1);

            if ($result) {
                Log::info("已清理MQTT保留消息: 主题[{$topic}]");
                return true;
            } else {
                Log::error("清理MQTT保留消息失败: publish返回false，主题[{$topic}]");
                return false;
            }
        } catch (\Exception $e) {
            Log::error("清理MQTT保留消息异常: 主题[{$topic}] 错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量清理MQTT保留消息
     * @param array $topics 要清理的主题数组
     * @return array 清理结果 ['success' => [], 'failed' => []]
     */
    public function clearRetainedMessages(array $topics): array
    {
        $result = [
            'success' => [],
            'failed' => []
        ];

        if (empty($topics)) {
            return $result;
        }

        foreach ($topics as $topic) {
            if ($this->clearRetainedMessage($topic)) {
                $result['success'][] = $topic;
            } else {
                $result['failed'][] = $topic;
            }
        }

        Log::info("批量清理MQTT保留消息完成: 成功" . count($result['success']) . "个，失败" . count($result['failed']) . "个");

        return $result;
    }

    /**
     * 封包
     * @throws \Throwable
     */
    protected function pack(array $payload): string
    {
        if ($this->protocolVersion == 5) {
            return V5::pack($payload);
        }

        return V3::pack($payload);
    }

    /**
     * 解包
     * @throws \Throwable
     */
    protected function unpack(string $data): array
    {
        if ($this->protocolVersion == 5) {
            return V5::unpack($data);
        }

        return V3::unpack($data);
    }

    /**
     * 获取当前连接状态
     * @return bool 是否已连接
     */
    public function isConnected(): bool
    {
        return $this->connected && $this->client !== null;
    }

    /**
     * 转换v5版本的topics到v3
     * @param array $topics
     * @param array $properties
     * @return array
     */
    private function transferTopics2V3(array $topics, array $properties = []): array
    {
        // 从v5格式中提取qos值作为v3格式的值
        return array_map(function($options) {
            return $options['qos'] ?? 0;
        }, $topics);
    }

    /**
     * 析构函数
     * 确保在对象销毁时关闭MQTT连接
     */
    public function __destruct()
    {
        if ($this->connected && $this->client) {
            $this->disconnect();
        }
    }
}
