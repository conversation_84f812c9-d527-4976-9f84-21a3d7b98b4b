<?php
declare(strict_types = 1);

namespace app\common\service\redis;

use think\facade\Cache;
use think\facade\Config;
use think\facade\Log;

/**
 * Redis连接池服务
 * 使用Swoole官方RedisPool实现的Redis连接池服务
 * 集成连接池管理、监控统计、便捷操作于一体的Redis服务
 * <AUTHOR> <<EMAIL>>
 * @package app\common\service\redis
 */
class RedisPoolService
{
    /**
     * 单例实例
     * @var ?RedisPoolService
     */
    private static ?RedisPoolService $instance = null;

    /**
     * Swoole官方Redis连接池
     * @var \Swoole\Database\RedisPool|null
     */
    private ?\Swoole\Database\RedisPool $pool = null;

    /**
     * 连接池大小
     * @var int
     */
    private int $poolSize = 10;

    /**
     * 是否启用监控
     * @var bool
     */
    private bool $enableMonitoring = true;

    /**
     * Redis配置
     * @var array
     */
    private array $config;

    /**
     * 统计数据
     * @var array
     */
    private array $stats = [
        // 操作统计
        'total_operations' => 0,
        'successful_operations' => 0,
        'failed_operations' => 0,
        'total_execution_time' => 0.0,
        'avg_execution_time' => 0.0,
        // 连接统计
        'connection_gets' => 0,
        'connection_puts' => 0,
        'connection_failures' => 0,
        'connection_times' => 0,
        'active_connections' => 0,
    ];

    /**
     * 构造函数
     * @param int $poolSize 连接池大小，0表示自动计算
     */
    private function __construct(int $poolSize = 0)
    {
        $this->config = Config::get('cache.stores.redis', []);

        // 如果未指定连接池大小，则根据CPU核心数动态计算
        if ($poolSize <= 0) {
            // 根据系统负载动态调整连接池大小：CPU核心数 * 5，最小10个，最大20个
            $poolSize = min(20, max(10, swoole_cpu_num() * 5));
        }

        $this->poolSize = $poolSize;
    }

    /**
     * 获取单例实例
     * @param int $poolSize 连接池大小（仅在首次创建时有效），0表示自动根据CPU核心数计算
     * @return self
     */
    public static function getInstance(int $poolSize = 0): self
    {
        if (self::$instance === null) {
            self::$instance = new self($poolSize);
        }

        return self::$instance;
    }

    /**
     * 初始化连接池
     */
    private function initPool(): void
    {
        if ($this->pool !== null) {
            return;
        }

        // 检查是否支持Swoole官方连接池
        if (class_exists('\Swoole\Database\RedisPool') && class_exists('\Swoole\Database\RedisConfig')) {
            $this->initSwooleRedisPool();
        } else {
            throw new \RuntimeException('Swoole RedisPool 不可用，请确保安装了正确版本的Swoole扩展');
        }
    }

    /**
     * 初始化Swoole官方Redis连接池
     */
    private function initSwooleRedisPool(): void
    {
        try {
            $config = new \Swoole\Database\RedisConfig();
            $config->withHost($this->config['host'] ?? '127.0.0.1')
                   ->withPort((int)($this->config['port'] ?? 6379))
                   ->withTimeout((float)($this->config['timeout'] ?? 3.0))
                   ->withDbIndex((int)($this->config['select'] ?? 0));

            // 设置密码（如果有）
            $password = $this->config['password'] ?? '';
            if (!empty($password)) {
                $config->withAuth($password);
            }

            // 注意：某些Swoole版本的RedisConfig不支持withOptions方法
            // 序列化器和前缀将在连接创建后通过Redis实例设置

            $this->pool = new \Swoole\Database\RedisPool($config, $this->poolSize);

            if ($this->enableMonitoring) {
                $this->stats['connection_times'] += $this->poolSize;
            }

            Log::info("使用Swoole官方Redis连接池，大小: {$this->poolSize}");
        } catch (\Exception $e) {
            Log::error('初始化Swoole Redis连接池失败: ' . $e->getMessage());
            throw new \RuntimeException('Redis连接池初始化失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取Redis连接
     * @param  float $timeout 超时时间
     * @throws \Exception
     * @return ?Object
     */
    private function getConnection(float $timeout = 3.0): ?Object
    {
        try {
            if ($this->enableMonitoring) {
                $this->stats['connection_gets']++;
            }

            // 检查是否在协程环境中
            $coroutineId = class_exists('\Swoole\Coroutine') ? \Swoole\Coroutine::getCid() : -1;
            if ($coroutineId !== -1) {
                // 动态初始化连接池
                if ($this->pool === null) {
                    $this->initPool();
                }

                // 使用Swoole官方连接池获取连接
                $redis = $this->pool->get($timeout);

                if (!$redis) {
                    if ($this->enableMonitoring) {
                        $this->stats['connection_failures']++;
                    }
                    Log::warning('从Swoole Redis连接池获取连接失败');
                    return null;
                }

                try {
                    // 与ThinkPHP的Redis缓存配置保持一致，不使用序列化选项
                    //$redis->setOption(\Redis::OPT_SERIALIZER, \Redis::SERIALIZER_PHP);

                    // 设置Redis选项（仅设置前缀）
                    $prefix = $this->config['prefix'] ?? '';
                    if (!empty($prefix)) {
                        $redis->setOption(\Redis::OPT_PREFIX, $prefix);
                    }
                } catch (\Exception $e) {
                    // 选项设置失败不影响基本功能，记录调试日志即可
                    Log::debug('设置Redis选项失败: ' . $e->getMessage());
                }

                if ($this->enableMonitoring) {
                    $this->stats['active_connections']++;
                }

                return $redis;
            }

            // 非协程环境，使用ThinkPHP的连接
            return Cache::store('redis')->handler();
        } catch (\Exception $e) {
            if ($this->enableMonitoring) {
                $this->stats['connection_failures']++;
            }
            Log::error('获取Redis连接异常: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 释放Redis连接
     * @param \Redis $redis
     */
    private function releaseConnection(\Redis $redis): void
    {
        // 只有在协程环境中且使用连接池时才需要归还连接
        if ($this->pool && class_exists('\Swoole\Coroutine') && \Swoole\Coroutine::getCid() !== -1) {
            try {
                // 使用Swoole官方连接池归还连接
                $this->pool->put($redis);

                if ($this->enableMonitoring) {
                    $this->stats['connection_puts']++;
                    $this->stats['active_connections'] = max(0, $this->stats['active_connections'] - 1);
                }
            } catch (\Throwable $e) {
                if ($this->enableMonitoring) {
                    $this->stats['connection_failures']++;
                }
                Log::debug('归还Redis连接异常: ' . $e->getMessage());
            }
        } else {
            // 非协程环境的连接由ThinkPHP自动管理，但我们仍然记录为"归还"以保持统计一致性
            if ($this->enableMonitoring) {
                $this->stats['connection_puts']++;
            }
        }
    }

    /**
     * 执行Redis操作
     * 自动管理连接的获取和归还
     * @param callable $callback Redis操作回调函数
     * @param float $timeout 获取连接超时时间
     * @return mixed 操作结果
     * @throws \Exception
     */
    public function execute(callable $callback, float $timeout = 3.0)
    {
        $startTime = microtime(true);
        $redis = null;
        $success = false;

        try {
            if ($this->enableMonitoring) {
                $this->stats['total_operations']++;
            }

            $redis = $this->getConnection($timeout);

            if (!$redis) {
                throw new \Exception('无法获取Redis连接');
            }

            $result = $callback($redis);
            $success = true;

            return $result;
        } catch (\Exception $e) {
            if ($this->enableMonitoring) {
                $this->stats['failed_operations']++;
                Log::warning('Redis操作失败: ' . $e->getMessage());
            }
            throw $e;
        } finally {
            if ($redis) {
                $this->releaseConnection($redis);
            }

            if ($this->enableMonitoring) {
                $executionTime = microtime(true) - $startTime;
                $this->updateExecutionStats($executionTime, $success);
            }
        }
    }

    /**
     * 执行Redis事务操作
     * @param callable $callback 事务操作回调函数
     * @param float $timeout 获取连接超时时间
     * @return mixed 事务执行结果
     * @throws \Exception
     */
    public function transaction(callable $callback, float $timeout = 3.0)
    {
        return $this->execute(function($redis) use ($callback) {
            $redis->multi();

            try {
                $callback($redis);
                return $redis->exec();
            } catch (\Throwable $e) {
                $redis->discard();
                throw $e;
            }
        }, $timeout);
    }

    /**
     * 执行Redis管道操作
     * @param callable $callback 管道操作回调函数
     * @param float $timeout 获取连接超时时间
     * @return mixed 管道执行结果
     * @throws \Exception
     */
    public function pipeline(callable $callback, float $timeout = 3.0)
    {
        return $this->execute(function($redis) use ($callback) {
            $pipeline = $redis->pipeline();
            $callback($pipeline);
            return $pipeline->exec();
        }, $timeout);
    }

    /**
     * 更新执行统计
     * @param float $executionTime
     * @param bool $success
     */
    private function updateExecutionStats(float $executionTime, bool $success): void
    {
        if ($success) {
            $this->stats['successful_operations']++;
        }

        $this->stats['total_execution_time'] += $executionTime;

        // 计算平均执行时间
        if ($this->stats['total_operations'] > 0) {
            $this->stats['avg_execution_time'] = $this->stats['total_execution_time'] / $this->stats['total_operations'];
        }
    }

    /**
     * 计算成功率
     * @return float
     */
    private function calculateSuccessRate(): float
    {
        if ($this->stats['total_operations'] === 0) {
            return 0.0;
        }

        return round(($this->stats['successful_operations'] / $this->stats['total_operations']) * 100, 2);
    }

    /**
     * 获取连接池状态
     * @return array
     */
    public function getStats(): array
    {
        if ($this->pool) {
            // Swoole官方连接池统计
            $poolStats = [
                'pool_size' => $this->poolSize,
                'current_count' => $this->poolSize, // 官方连接池不提供当前连接数详情
                'available' => $this->poolSize, // 官方连接池不提供可用连接数详情
                'waiting' => 0, // 官方连接池不提供等待数详情
                'connection_times' => $this->stats['connection_times'],
                'active_connections' => $this->stats['active_connections'],
                'pool_type' => 'Swoole官方连接池',
            ];
        } else {
            $poolStats = [
                'pool_size' => 0,
                'current_count' => 0,
                'available' => 0,
                'waiting' => 0,
                'connection_times' => $this->stats['connection_times'],
                'active_connections' => $this->stats['active_connections'],
                'pool_type' => '无连接池',
            ];
        }

        // 合并连接池统计和操作统计
        return array_merge($poolStats, [
            'monitoring_enabled' => $this->enableMonitoring,
            'operation_stats' => [
                'total_operations' => $this->stats['total_operations'],
                'successful_operations' => $this->stats['successful_operations'],
                'failed_operations' => $this->stats['failed_operations'],
                'total_execution_time' => $this->stats['total_execution_time'],
                'avg_execution_time' => $this->stats['avg_execution_time'],
                'connection_gets' => $this->stats['connection_gets'],
                'connection_puts' => $this->stats['connection_puts'],
                'connection_failures' => $this->stats['connection_failures'],
            ],
            'success_rate' => $this->calculateSuccessRate(),
            'avg_execution_time_ms' => round($this->stats['avg_execution_time'] * 1000, 2),
        ]);
    }

    /**
     * 获取监控报告
     * @return string
     */
    public function getMonitoringReport(): string
    {
        $stats = $this->getStats();

        $swooleExists = class_exists('\Swoole\Coroutine');
        $coroutineId = $swooleExists ? \Swoole\Coroutine::getCid() : -1;
        $hasPool = $this->pool !== null;
        $isCoroutineEnv = $hasPool && $swooleExists && $coroutineId !== -1;
        $envType = $isCoroutineEnv ? '协程环境' : '非协程环境';

        // 调试信息（可以在需要时启用）
        Log::debug("Redis连接池环境检测: Swoole存在={$swooleExists}, 协程ID={$coroutineId}, 连接池存在={$hasPool}, 判定为协程环境={$isCoroutineEnv}");

        $report = "=== Redis连接池监控报告 ===\n";
        $report .= "监控状态: " . ($stats['monitoring_enabled'] ? '启用' : '禁用') . "\n";
        $report .= "运行环境: {$envType}\n";

        if ($isCoroutineEnv) {
            $report .= "\n--- 连接池状态 ---\n";
            $report .= "连接池大小: {$stats['pool_size']}\n";
            $report .= "当前连接数: {$stats['current_count']}\n";
            $report .= "可用连接数: {$stats['available']}\n";
            $report .= "等待连接数: {$stats['waiting']}\n";
            $report .= "活跃连接数: {$stats['active_connections']}\n";
        } else {
            $report .= "\n--- 连接状态 ---\n";
            $report .= "连接类型: ThinkPHP默认连接\n";
            $report .= "连接管理: 框架自动管理\n";
        }

        $report .= "\n--- 操作统计 ---\n";
        $report .= "总操作数: {$stats['operation_stats']['total_operations']}\n";
        $report .= "成功操作数: {$stats['operation_stats']['successful_operations']}\n";
        $report .= "失败操作数: {$stats['operation_stats']['failed_operations']}\n";
        $report .= "成功率: {$stats['success_rate']}%\n";
        $report .= "平均执行时间: {$stats['avg_execution_time_ms']}ms\n";
        $report .= "\n--- 连接统计 ---\n";
        $report .= "连接获取次数: {$stats['operation_stats']['connection_gets']}\n";
        $report .= "连接归还次数: {$stats['operation_stats']['connection_puts']}\n";
        $report .= "连接失败次数: {$stats['operation_stats']['connection_failures']}\n";

        return $report;
    }

    /**
     * 启用/禁用监控
     * @param bool $enable
     */
    public function setMonitoring(bool $enable): void
    {
        $this->enableMonitoring = $enable;
    }

    /**
     * 重置统计数据
     */
    public function resetStats(): void
    {
        $this->stats = [
            'total_operations' => 0,
            'successful_operations' => 0,
            'failed_operations' => 0,
            'total_execution_time' => 0.0,
            'avg_execution_time' => 0.0,
            'connection_gets' => 0,
            'connection_puts' => 0,
            'connection_failures' => 0,
            'connection_times' => 0,
            'active_connections' => 0,
        ];
    }

    /**
     * 关闭连接池
     */
    public function close(): void
    {
        if ($this->pool) {
            // Swoole官方连接池会自动管理连接关闭
            $this->pool->close();
            $this->pool = null;
            $this->stats['active_connections'] = 0;
        }
    }

    // ==================== 便捷方法 ====================

    /**
     * 便捷方法：设置键值
     * @param string $key
     * @param mixed $value
     * @param int $expire
     * @return bool
     */
    public function set(string $key, $value, int $expire = 0): bool
    {
        return $this->execute(function($redis) use ($key, $value, $expire) {
            if ($expire > 0) {
                return $redis->setex($key, $expire, $value);
            }
            return $redis->set($key, $value);
        });
    }

    /**
     * 便捷方法：获取键值
     * @param string $key
     * @return mixed
     */
    public function get(string $key)
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->get($key);
        });
    }

    /**
     * 便捷方法：删除键
     * @param string|array $key
     * @return int
     */
    public function del($key): int
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->del($key);
        });
    }

    /**
     * 便捷方法：检查键是否存在
     * @param string $key
     * @return bool
     */
    public function exists(string $key): bool
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->exists($key) > 0;
        });
    }

    /**
     * 便捷方法：设置过期时间
     * @param string $key
     * @param int $seconds
     * @return bool
     */
    public function expire(string $key, int $seconds): bool
    {
        return $this->execute(function($redis) use ($key, $seconds) {
            return $redis->expire($key, $seconds);
        });
    }

    /**
     * 便捷方法：哈希表操作
     * @param string $key
     * @param string $field
     * @param mixed $value
     * @return bool
     */
    public function hSet(string $key, string $field, $value): bool
    {
        return $this->execute(function($redis) use ($key, $field, $value) {
            return $redis->hSet($key, $field, $value);
        });
    }

    /**
     * 便捷方法：获取哈希表字段值
     * @param string $key
     * @param string $field
     * @return mixed
     */
    public function hGet(string $key, string $field)
    {
        return $this->execute(function($redis) use ($key, $field) {
            return $redis->hGet($key, $field);
        });
    }

    /**
     * 便捷方法：获取哈希表所有字段
     * @param string $key
     * @return array
     */
    public function hGetAll(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->hGetAll($key) ?: [];
        });
    }

    /**
     * 便捷方法：集合添加成员
     * @param string $key
     * @param mixed ...$values
     * @return int
     */
    public function sAdd(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sAdd($key, ...$values);
        });
    }

    /**
     * 便捷方法：获取集合所有成员
     * @param string $key
     * @return array
     */
    public function sMembers(string $key): array
    {
        return $this->execute(function($redis) use ($key) {
            return $redis->sMembers($key) ?: [];
        });
    }

    /**
     * 便捷方法：从集合中移除成员
     * @param string $key
     * @param mixed ...$values
     * @return int
     */
    public function sRem(string $key, ...$values): int
    {
        return $this->execute(function($redis) use ($key, $values) {
            return $redis->sRem($key, ...$values);
        });
    }

    /**
     * 便捷方法：有序集合添加成员
     * @param string $key
     * @param float $score
     * @param mixed $value
     * @return int
     */
    public function zAdd(string $key, float $score, $value): int
    {
        return $this->execute(function($redis) use ($key, $score, $value) {
            return $redis->zAdd($key, $score, $value);
        });
    }

    /**
     * 便捷方法：获取有序集合成员分数
     * @param string $key
     * @param mixed $member
     * @return float|false
     */
    public function zScore(string $key, $member)
    {
        return $this->execute(function($redis) use ($key, $member) {
            return $redis->zScore($key, $member);
        });
    }

    /**
     * 便捷方法：按分数范围获取有序集合成员
     * @param string $key
     * @param string $min
     * @param string $max
     * @return array
     */
    public function zRangeByScore(string $key, string $min, string $max): array
    {
        return $this->execute(function($redis) use ($key, $min, $max) {
            return $redis->zRangeByScore($key, $min, $max) ?: [];
        });
    }

    /**
     * 便捷方法：从有序集合中移除成员
     * @param string $key
     * @param mixed ...$members
     * @return int
     */
    public function zRem(string $key, ...$members): int
    {
        return $this->execute(function($redis) use ($key, $members) {
            return $redis->zRem($key, ...$members);
        });
    }

    /**
     * 便捷方法：使用HSCAN遍历哈希表
     * @param string $key
     * @param int $cursor
     * @param string|null $pattern
     * @param int $count
     * @return array
     */
    public function hScan(string $key, int &$cursor, ?string $pattern = null, int $count = 10): array
    {
        return $this->execute(function($redis) use ($key, &$cursor, $pattern, $count) {
            return $redis->hScan($key, $cursor, $pattern, $count);
        });
    }

    /**
     * 便捷方法：删除哈希表字段
     * @param string $key
     * @param string ...$fields
     * @return int
     */
    public function hDel(string $key, ...$fields): int
    {
        return $this->execute(function($redis) use ($key, $fields) {
            return $redis->hDel($key, ...$fields);
        });
    }

    /**
     * 便捷方法：开启Redis管道（MULTI PIPELINE）
     * 注意：此方法仅用于单独的管道操作，建议使用 pipeline() 方法进行完整的管道操作
     * @return mixed
     */
    public function multi()
    {
        return $this->execute(function($redis) {
            return $redis->multi(\Redis::PIPELINE);
        });
    }

    /**
     * 便捷方法：执行Redis管道（EXEC）
     * 注意：此方法仅用于单独的管道操作，建议使用 pipeline() 方法进行完整的管道操作
     * @return array
     */
    public function exec(): array
    {
        return $this->execute(function($redis) {
            return $redis->exec() ?: [];
        });
    }
}
