<?php
declare(strict_types = 1);

namespace app\tenant\service;

use app\common\service\redis\RedisPoolService;
use think\facade\Cache;
use think\facade\Log;

/**
 * 设备状态服务类
 * 用于管理设备的在线状态
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\service
 */
class DeviceStatusService extends BaseService
{
    /**
     * 单例实例
     * @var ?DeviceStatusService
     */
    private static ?DeviceStatusService $instance = null;

    private const STATUS_ONLINE = 'online';

    private const STATUS_OFFLINE = 'offline';

    private const STATUS_ALIVE = 'alive'; // 'alive' 被视为一种活动状态

    private const DEFAULT_TENANT_CODE = 'default';



    private const CLEANUP_BATCH_SIZE = 100;

    private const CLEANUP_MAX_PROCESS_TIME = 10; // 秒

    /**
     * Redis键前缀
     * @var string
     */
    protected string $redisKeyPrefix = 'helio:tenant:device:';

    /**
     * 设备状态Hash键模板（按租户分组）
     * @var string
     */
    protected string $deviceStatusKeyTpl = 'status:%s';

    /**
     * 设备活跃时间Sorted Set键模板（按租户分组）
     * @var string
     */
    protected string $deviceActiveKeyTpl = 'active:%s';

    /**
     * 设备状态过期时间（秒）
     * 如果在这段时间内没有收到心跳，则认为设备离线
     * @var int
     */
    protected int $deviceStatusExpire = 120; // 2分钟

    /**
     * 离线设备状态信息过期时间（秒）
     * 离线设备的状态信息保留时间
     * @var int
     */
    protected int $offlineStatusExpire = 604800; // 7天

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取单例实例
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Redis连接池服务实例
     * @var \app\common\service\redis\RedisPoolService|null
     */
    protected ?RedisPoolService $redisPoolService = null;

    /**
     * 获取Redis连接池服务实例
     * @return RedisPoolService
     */
    protected function getRedisPoolService(): RedisPoolService
    {
        if ($this->redisPoolService === null) {
            $this->redisPoolService = RedisPoolService::getInstance();
        }

        return $this->redisPoolService;
    }

    /**
     * 获取设备状态
     * @param string $deviceId 设备ID
     * @param string $tenantCode 租户编码
     * @return array 设备状态信息
     */
    public function getDeviceStatus(string $deviceId, string $tenantCode = self::DEFAULT_TENANT_CODE): array
    {
        try {
            $redisPoolService = $this->getRedisPoolService();

            // 设备状态键
            $deviceStatusKey = $this->redisKeyPrefix . sprintf($this->deviceStatusKeyTpl, $tenantCode);

            // 设备活跃键
            $deviceActiveKey = $this->redisKeyPrefix . sprintf($this->deviceActiveKeyTpl, $tenantCode);

            // 获取设备状态
            $statusJson = $redisPoolService->hGet($deviceStatusKey, $deviceId);

            if (!$statusJson) {
                return [
                    'deviceId'      => $deviceId,
                    'tenantCode'    => $tenantCode,
                    'status'        => self::STATUS_OFFLINE, // 默认为离线状态
                    'online'        => false,
                    'lastHeartbeat' => 0,
                ];
            }

            $status = json_decode($statusJson, true);

            // 获取设备最后活跃时间
            $lastActiveTime = $redisPoolService->zScore($deviceActiveKey, $deviceId);
            $now            = time();

            // 检查设备是否在线（最后活跃时间在有效期内）
            $isOnline = $lastActiveTime && ($now - $lastActiveTime <= $this->deviceStatusExpire);

            // 如果设备不在线，但状态是online或alive，则更新状态为offline
            if (!$isOnline && in_array($status['status'], [self::STATUS_ONLINE, self::STATUS_ALIVE])) {
                $status['status'] = self::STATUS_OFFLINE;
            }

            // 添加在线状态标志
            $status['online'] = $isOnline;

            return $status;
        } catch (\Exception $e) {
            Log::error('获取设备状态异常: ' . $e->getMessage());

            return [
                'deviceId'      => $deviceId,
                'tenantCode'    => $tenantCode,
                'status'        => self::STATUS_OFFLINE, // 默认为离线状态
                'online'        => false,
                'lastHeartbeat' => 0,
            ];
        }
    }

    /**
     * 批量获取设备状态
     * @param array $deviceIds 设备ID数组
     * @param string $tenantCode 租户编码
     * @return array 设备状态信息数组，键为设备ID，值为状态信息
     */
    public function batchGetDeviceStatus(array $deviceIds, string $tenantCode = self::DEFAULT_TENANT_CODE): array
    {
        if (empty($deviceIds)) {
            return [];
        }

        return $this->getBatchDeviceStatus($deviceIds, $tenantCode);
    }

    /**
     * 批量获取设备状态（batchGetDeviceStatus的别名方法）
     * @param array $deviceIds 设备ID数组
     * @param string $tenantCode 租户编码
     * @return array 设备状态信息数组，键为设备ID，值为状态信息
     */
    public function getBatchDeviceStatus(array $deviceIds, string $tenantCode = self::DEFAULT_TENANT_CODE): array
    {
        if (empty($deviceIds)) {
            return [];
        }

        try {
            $redisPoolService = $this->getRedisPoolService();
            $result = [];
            $now    = time();

            // 构建设备在线集合键
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            // 获取在线设备集合
            $onlineDevicesRaw = $redisPoolService->sMembers($onlineKey) ?: [];

            // 转换为整数数组（不再需要处理序列化格式）
            $onlineDevices = array_map('intval', $onlineDevicesRaw);

            foreach ($deviceIds as $deviceId) {
                // 构建设备状态键（与DeviceStatusMonitorCommand保持一致）
                $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";

                // 获取设备状态
                $status = $redisPoolService->hGetAll($statusKey);

                if (empty($status)) {
                    $result[$deviceId] = [
                        'deviceId'      => $deviceId,
                        'tenantCode'    => $tenantCode,
                        'status'        => self::STATUS_OFFLINE,
                        'online'        => false,
                        'lastHeartbeat' => 0,
                    ];
                    continue;
                }

                // 检查设备是否在线（从在线集合中检查）
                $isOnline = in_array((int)$deviceId, $onlineDevices);

                // 如果设备不在线，但状态是online或alive，则更新状态为offline
                if (!$isOnline && in_array($status['status'] ?? '', [self::STATUS_ONLINE, self::STATUS_ALIVE])) {
                    $status['status'] = self::STATUS_OFFLINE;
                }

                // 添加在线状态标志
                $status['online'] = $isOnline;

                // 确保返回的数据结构一致
                $result[$deviceId] = [
                    'deviceId'      => $status['deviceId'] ?? $deviceId,
                    'tenantCode'    => $status['tenantCode'] ?? $tenantCode,
                    'status'        => $status['status'] ?? self::STATUS_OFFLINE,
                    'online'        => $isOnline,
                    'lastHeartbeat' => $status['lastHeartbeat'] ?? 0,
                ];
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('批量获取设备状态异常: ' . $e->getMessage());

            // 返回默认状态
            $result = [];

            foreach ($deviceIds as $deviceId) {
                $result[$deviceId] = [
                    'deviceId'      => $deviceId,
                    'tenantCode'    => $tenantCode,
                    'status'        => self::STATUS_OFFLINE,
                    'online'        => false,
                    'lastHeartbeat' => 0,
                ];
            }

            return $result;
        }
    }

    /**
     * 获取租户的所有在线设备
     * @param string $tenantCode 租户编码
     * @return array 在线设备ID数组
     */
    public function getTenantOnlineDevices(string $tenantCode = self::DEFAULT_TENANT_CODE): array
    {
        try {
            $redisPoolService = $this->getRedisPoolService();

            // 设备活跃键
            $deviceActiveKey = $this->redisKeyPrefix . sprintf($this->deviceActiveKeyTpl, $tenantCode);

            // 获取当前时间
            $now = time();

            // 计算活跃时间下限（当前时间减去设备状态过期时间）
            $minActiveTime = $now - $this->deviceStatusExpire;

            // 获取活跃时间在有效期内的设备（在线设备）
            $onlineDevices = $redisPoolService->zRangeByScore($deviceActiveKey, (string)$minActiveTime, '+inf');

            // 确保返回数组类型
            if (!is_array($onlineDevices)) {
                Log::warning("Redis zRangeByScore 返回非数组类型，租户: {$tenantCode}");

                return [];
            }

            return $onlineDevices;
        } catch (\Exception $e) {
            Log::error('获取租户在线设备异常: ' . $e->getMessage());

            return [];
        }
    }

    /**
     * 获取设备在线时长
     * @param string $deviceId 设备ID
     * @param string $tenantCode 租户编码
     * @return int 在线时长（秒）
     */
    public function getDeviceOnlineTime(string $deviceId, string $tenantCode = self::DEFAULT_TENANT_CODE): int
    {
        try {
            $redisPoolService = $this->getRedisPoolService();

            // 设备活跃键
            $deviceActiveKey = $this->redisKeyPrefix . sprintf($this->deviceActiveKeyTpl, $tenantCode);

            // 获取设备最后活跃时间
            $lastActiveTime = $redisPoolService->zScore($deviceActiveKey, $deviceId);

            if (!$lastActiveTime) {
                return 0;
            }

            // 检查设备是否在线
            $now = time();

            if ($now - $lastActiveTime > $this->deviceStatusExpire) {
                return 0; // 设备离线
            }

            // 计算在线时长（从最后活跃时间开始）
            return $now - $lastActiveTime;
        } catch (\Exception $e) {
            Log::error('获取设备在线时长异常: ' . $e->getMessage());

            return 0;
        }
    }

    /**
     * 格式化设备在线时长
     * @param string $deviceId 设备ID
     * @param string $tenantCode 租户编码
     * @return string 格式化后的在线时长
     */
    public function getFormattedOnlineTime(string $deviceId, string $tenantCode = self::DEFAULT_TENANT_CODE): string
    {
        $onlineTime = $this->getDeviceOnlineTime($deviceId, $tenantCode);

        if ($onlineTime <= 0) {
            return '离线';
        }

        // 使用format_seconds函数格式化在线时长
        if (function_exists('format_seconds')) {
            return format_seconds($onlineTime);
        }

        // 简单格式化
        if ($onlineTime < 60) {
            return $onlineTime . '秒';
        } elseif ($onlineTime < 3600) {
            return floor($onlineTime / 60) . '分钟';
        } elseif ($onlineTime < 86400) {
            return floor($onlineTime / 3600) . '小时';
        }

        return floor($onlineTime / 86400) . '天';
    }

    /**
     * 批量更新设备状态
     * 用于一次性更新多个设备的状态，减少Redis连接和操作次数
     * @param array $devices 设备数组，每个元素包含 deviceId 和 tenantCode
     * @param string $status 状态
     * @param int $timestamp 时间戳
     * @return bool 是否成功
     */
    public function batchUpdateDeviceStatus(array $devices, string $status, int $timestamp): bool
    {
        if (empty($devices)) {
            return true;
        }

        try {
            $redisPoolService = $this->getRedisPoolService();

            // 按租户分组设备
            $devicesByTenant = [];

            foreach ($devices as $device) {
                $tenantCode = $device['tenantCode'] ?? self::DEFAULT_TENANT_CODE;
                $deviceId   = $device['deviceId']   ?? '';

                if (empty($deviceId)) {
                    continue;
                }

                if (!isset($devicesByTenant[$tenantCode])) {
                    $devicesByTenant[$tenantCode] = [];
                }

                $devicesByTenant[$tenantCode][] = $deviceId;
            }

            // 使用管道批量处理
            $redisPoolService->multi(\Redis::PIPELINE);

            foreach ($devicesByTenant as $tenantCode => $deviceIds) {
                // 设备状态键
                $deviceStatusKey = $this->redisKeyPrefix . sprintf($this->deviceStatusKeyTpl, $tenantCode);

                // 设备活跃键
                $deviceActiveKey = $this->redisKeyPrefix . sprintf($this->deviceActiveKeyTpl, $tenantCode);

                foreach ($deviceIds as $deviceId) {
                    // 更新设备状态
                    $statusData = [
                        'deviceId'      => $deviceId,
                        'tenantCode'    => $tenantCode,
                        'status'        => $status,
                        'lastHeartbeat' => $timestamp,
                        'updatedAt'     => $timestamp,
                    ];

                    // 更新设备状态
                    $redisPoolService->hSet($deviceStatusKey, $deviceId, json_encode($statusData));

                    // 更新设备活跃时间
                    $redisPoolService->zAdd($deviceActiveKey, $timestamp, $deviceId);
                }
            }

            // 执行管道命令
            $redisPoolService->exec();

            return true;
        } catch (\Throwable $e) {
            Log::error('批量更新设备状态异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 更新设备状态
     * @param string $tenantCode 租户编码
     * @param int $deviceId 设备ID
     * @param string $status 状态
     * @param int $timestamp 时间戳
     * @return bool 是否更新成功
     */
    public function updateDeviceStatus(string $tenantCode, int $deviceId, string $status, int $timestamp): bool
    {
        try {
            $redisPoolService = $this->getRedisPoolService();

            // 设备状态键
            $deviceStatusKey = $this->redisKeyPrefix . sprintf($this->deviceStatusKeyTpl, $tenantCode);

            // 设备活跃键
            $deviceActiveKey = $this->redisKeyPrefix . sprintf($this->deviceActiveKeyTpl, $tenantCode);

            // 设备状态信息
            $statusData = [
                'deviceId'      => $deviceId,
                'tenantCode'    => $tenantCode,
                'status'        => $status,
                'lastHeartbeat' => $timestamp,
                'updatedAt'     => time(),
            ];

            // 不使用管道，直接执行命令
            // 更新设备状态
            $result1 = $redisPoolService->hSet($deviceStatusKey, (string)$deviceId, json_encode($statusData, JSON_UNESCAPED_UNICODE));

            // 更新设备活跃时间
            $result2 = true;

            if ($status === self::STATUS_ONLINE || $status === self::STATUS_ALIVE) {
                // 设备在线，更新活跃时间
                $result2 = $redisPoolService->zAdd($deviceActiveKey, $timestamp, $deviceId);
            } elseif ($status === self::STATUS_OFFLINE) {
                // 设备离线，从活跃集合中移除
                $result2 = $redisPoolService->zRem($deviceActiveKey, $deviceId);
            }

            // 检查操作结果
            if ($result1 === false || $result2 === false) {
                throw new \Exception('Redis operation failed');
            }

            return true;
        } catch (\Exception $e) {
            Log::error('更新设备状态异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 清理长期离线的设备状态
     * @param string $tenantCode 租户编码
     * @param int $offlineDays 离线天数
     * @return int 清理的设备数量
     */
    public function cleanupOfflineDevices(string $tenantCode = self::DEFAULT_TENANT_CODE, int $offlineDays = 7): int
    {
        try {
            $redisPoolService = $this->getRedisPoolService();

            // 设备状态键
            $deviceStatusKey = $this->redisKeyPrefix . sprintf($this->deviceStatusKeyTpl, $tenantCode);

            // 设备活跃键
            $deviceActiveKey = $this->redisKeyPrefix . sprintf($this->deviceActiveKeyTpl, $tenantCode);

            // 获取当前时间
            $now = time();

            // 计算离线时间阈值
            $offlineThreshold = $now - ($offlineDays * 86400);

            // 获取所有设备状态
            $count = 0;

            // 使用游标方式遍历所有设备，确保每个设备都会被处理到
            $cursor         = 0;
            $batchSize      = self::CLEANUP_BATCH_SIZE; // 每批处理的设备数量
            $maxProcessTime = self::CLEANUP_MAX_PROCESS_TIME; // 最大处理时间（秒），避免处理时间过长
            $startTime      = time();
            $processedKeys  = []; // 记录已处理的键，避免重复处理

            do {
                // 检查是否超过最大处理时间
                if (time() - $startTime > $maxProcessTime) {
                    Log::info("清理离线设备达到最大处理时间限制，已处理 {$count} 个设备，下次继续处理");
                    break;
                }

                // 使用HSCAN分批获取设备状态，确保每个设备最终都会被处理
                $scanResult = $redisPoolService->hScan($deviceStatusKey, $cursor, null, $batchSize);

                if (!is_array($scanResult) || count($scanResult) < 2) {
                    // 扫描结果异常，退出循环
                    Log::warning('HSCAN返回异常结果，退出清理循环');
                    break;
                }

                [$cursor, $result] = $scanResult;

                // 如果没有获取到数据，但游标不为0，继续下一轮扫描
                if (empty($result) && $cursor != 0) {
                    continue;
                }

                // 如果没有获取到数据且游标为0，说明已经扫描完所有数据，退出循环
                if (empty($result) && $cursor == 0) {
                    break;
                }

                // 处理获取到的设备状态
                foreach ($result as $deviceId => $statusJson) {
                    // 如果已经处理过该设备，跳过
                    if (isset($processedKeys[$deviceId])) {
                        continue;
                    }

                    // 标记为已处理
                    $processedKeys[$deviceId] = true;

                    $status = json_decode($statusJson, true);

                    // 检查设备是否长期离线
                    if (isset($status['status']) && $status['status'] === self::STATUS_OFFLINE && isset($status['updatedAt']) && $status['updatedAt'] < $offlineThreshold) {
                        // 删除长期离线的设备状态
                        $redisPoolService->hDel($deviceStatusKey, $deviceId);

                        // 确保设备从活跃集合中移除（虽然正常情况下已经移除了）
                        $redisPoolService->zRem($deviceActiveKey, $deviceId);

                        // 每处理一个设备让出协程，避免长时间占用
                        if (class_exists('\Swoole\Coroutine') && \Swoole\Coroutine::getCid() !== -1) {
                            \Swoole\Coroutine::sleep(0.001);
                        }

                        $count++;
                    }
                }

                // 如果处理的设备数量达到一定数量，记录日志
                if ($count > 0 && $count % 1000 === 0) {
                    Log::info("已清理 {$count} 个长期离线设备");
                }
            } while ($cursor != 0); // 当游标为0时，表示已经扫描完所有数据

            return $count;
        } catch (\Exception $e) {
            Log::error('清理离线设备异常: ' . $e->getMessage());

            return 0;
        }
    }
}
