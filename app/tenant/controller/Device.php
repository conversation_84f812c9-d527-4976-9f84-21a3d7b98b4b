<?php
declare(strict_types = 1);

namespace app\tenant\controller;

use app\common\service\ExportService;
use app\tenant\repository\DeviceRepository;
use app\tenant\repository\UserRoleRepository;
use app\tenant\validate\Device as DeviceValidate;
use think\App;
use think\Response;

/**
 * 设备管理控制器
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\controller
 */
class Device extends AuthBase
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 获取当前用户有权控制的设备列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @return Response
     */
    public function myList(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();
        // 添加租户编码到参数中
        $params['tenant_code'] = $this->getTenantCode();
        // 实例化设备仓库
        $deviceRepository = new DeviceRepository();

        // 判断是否为创始人
        if ($this->isFounder() === 1) {
            // 查询所有设备，并获取在线状态
            $result = $deviceRepository->getList($params, $pageNo, $pageSize, true);
        } else {
            // 获取当前用户的所有角色ID
            $roleIds = (new UserRoleRepository())->getUserRoleIds($this->getUserId());

            // 使用DeviceRepository中的方法获取数据，并获取在线状态
            $result = $deviceRepository->getUserDevices($params, $roleIds, $pageNo, $pageSize, true);
        }

        return $this->success($result);
    }

    /**
     * 设备管理列表
     * @return Response
     */
    public function list(): Response
    {
        $params   = $this->request->param();
        $pageNo   = $this->request->param('page', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 20, 'intval');
        $roleId   = $this->request->param('role_id', 0, 'intval');

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();
        // 添加租户编码到参数中
        $params['tenant_code'] = $this->getTenantCode();
        // 实例化设备仓库
        $deviceRepository = new DeviceRepository();

        // 如果传入了角色ID，获取该角色已分配和可分配的设备列表
        if ($roleId > 0) {
            $result = $deviceRepository->getAssignableList($params, $roleId, $pageNo, $pageSize, true);
        } else {
            $result = $deviceRepository->getList($params, $pageNo, $pageSize, true);
        }

        return $this->success($result);
    }

    /**
     * 获取设备信息
     * @return \think\Response
     */
    public function info(): Response
    {
        $id     = $this->request->param('id/d');
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();
        // 添加租户编码到参数中
        $params['tenant_code'] = $this->getTenantCode();

        // 查询或更新条件
        $condition = ['id' => $id, 'tenant_id' => $params['tenant_id']];

        $deviceRepository = new DeviceRepository();

        $info = $deviceRepository->getInfo($condition);

        // 验证当前数据是否存在
        if (empty($info)) {
            return $this->error('设备不存在');
        }

        // 添加设备在线状态
        if (!empty($params['tenant_code'])) {
            $deviceList = $deviceRepository->getDeviceOnlineStatus([$info], $params['tenant_code']);
            $info       = $deviceList[0] ?? $info;
        }

        return $this->success($info);
    }

    /**
     * 添加设备
     * @throws \Exception
     * @return \think\Response
     */
    public function add(): Response
    {
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        // 验证数据
        $validate = new DeviceValidate();

        if (!$validate->scene('add')->check($params)) {
            return $this->error($validate->getError());
        }

        $result = (new DeviceRepository())->addInfo($params);

        // 说明添加成功
        if (!empty($result->id)) {
            // 记录操作日志
            $this->logService->recordOperationLog(
                '添加设备',
                $result->toArray()
            );

            return $this->success('添加成功');
        }

        return $this->error('添加失败');
    }

    /**
     * 编辑设备
     * @throws \Exception
     * @return \think\Response
     */
    public function edit(): Response
    {
        $id     = $this->request->param('id/d');
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        // 查询或更新条件
        $condition = ['id' => $id, 'tenant_id' => $params['tenant_id'], ['status', '>=', 0]];

        $deviceRepository = new DeviceRepository();

        // 验证当前数据是否存在
        if (empty($deviceRepository->getInfo($condition))) {
            return $this->error('设备不存在');
        }

        // 验证数据
        $validate = new DeviceValidate();

        if (!$validate->scene('edit')->check($params)) {
            return $this->error($validate->getError());
        }

        // 更新设备信息
        $result = $deviceRepository->updateInfo($condition, $params);

        if ($result) {
            // 写入事件日志
            $this->logService->recordOperationLog(
                '编辑设备',
                ['id' => $id]
            );

            return $this->success('编辑成功');
        }

        return $this->error('编辑失败');
    }

    /**
     * 更新设备状态
     * @return Response
     */
    public function status(): Response
    {
        $id     = $this->request->param('id/d');
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        // 查询或更新条件
        $condition = ['id' => $id, 'tenant_id' => $params['tenant_id'], ['status', '>=', 0]];

        $deviceRepository = new DeviceRepository();

        // 验证当前数据是否存在
        if (empty($deviceRepository->getInfo($condition))) {
            return $this->error('设备不存在');
        }

        // 验证数据
        $validate = new DeviceValidate();

        if (!$validate->scene('status')->check($params)) {
            return $this->error($validate->getError());
        }

        // 更新设备状态
        $result = $deviceRepository->updateStatus($condition, (int)$params['status']);

        if ($result) {
            // 写入事件日志
            $this->logService->recordOperationLog(
                '更新设备状态',
                [
                    'id'     => $params['id'],
                    'status' => $params['status'],
                ]
            );

            return $this->success('状态更新成功');
        }

        return $this->error('状态更新失败');
    }

    /**
     * 删除设备（软删除）
     * @return Response
     */
    public function delete(): Response
    {
        $id     = $this->request->param('id');
        $params = $this->request->param();

        // 指定只能操作当租户ID
        $params['tenant_id'] = $this->getTenantId();

        // 查询或更新条件
        $condition = ['id' => $id, 'tenant_id' => $params['tenant_id'], ['status', '>=', 0]];

        $deviceRepository = new DeviceRepository();

        // 验证当前数据是否存在
        if (empty($deviceRepository->getInfo($condition))) {
            return $this->error('设备不存在');
        }

        // 验证数据
        $validate = new DeviceValidate();

        if (!$validate->scene('delete')->check($params)) {
            return $this->error($validate->getError());
        }

        $result = $deviceRepository->recycle($id);

        if ($result) {
            // 写入事件日志
            $this->logService->recordOperationLog(
                '软删除设备',
                ['id' => $id]
            );

            return $this->success('删除成功');
        }

        return $this->error('删除失败');
    }

    /**
     * 获取设备历史数据
     * @return Response
     */
    public function historyData(): Response
    {
        $params = $this->request->param();

        // 参数验证（简单示例，实际应使用验证器）
        if (empty($params['deviceId']) || empty($params['startTime']) || empty($params['endTime']) || empty($params['dataTypes'])) {
            return $this->error('参数错误');
        }

        $page     = $params['page']     ?? 1;
        $pageSize = $params['pageSize'] ?? 50;
        $interval = $params['interval'] ?? '10m'; // 默认10分钟

        // 模拟数据生成
        $chartData = [];
        $total     = 288; // 假设总数据点

        $currentTimestamp = strtotime($params['startTime']);
        $endTimestamp     = strtotime($params['endTime']);

        // 根据interval计算步长（秒）
        $intervalSeconds = 0;

        switch ($interval) {
            case '1m':
                $intervalSeconds = 60;
                break;
            case '5m':
                $intervalSeconds = 5 * 60;
                break;
            case '10m':
                $intervalSeconds = 10 * 60;
                break;
            case '30m':
                $intervalSeconds = 30 * 60;
                break;
            case '1h':
                $intervalSeconds = 60 * 60;
                break;
            case '6h':
                $intervalSeconds = 6 * 60 * 60;
                break;
            case '1d':
                $intervalSeconds = 24 * 60 * 60;
                break;
            default:
                $intervalSeconds = 10 * 60; // 默认10分钟
        }

        $dataCount  = 0;
        $startIndex = ($page - 1) * $pageSize;
        $endIndex   = $startIndex + $pageSize;

        while ($currentTimestamp <= $endTimestamp && $dataCount < $endIndex) {
            if ($dataCount >= $startIndex) {
                $dataPoint = [
                    'time'  => date('Y-m-d H:i:s', $currentTimestamp),
                    'layer' => $params['layer'] ?? null, // 如果提供了layer参数
                ];

                foreach ($params['dataTypes'] as $dataType) {
                    switch ($dataType) {
                        case 'flow':
                            $dataPoint['flow'] = round(rand(1200, 1300) / 10, 1);
                            break;
                        case 'innerPressure':
                            $dataPoint['innerPressure'] = round(rand(2000, 2500) / 100, 2);
                            break;
                        case 'outerPressure':
                            $dataPoint['outerPressure'] = round(rand(1800, 1900) / 100, 2);
                            break;
                        case 'temperature':
                            $dataPoint['temperature'] = round(rand(300, 380) / 10, 1);
                            break;
                        case 'valveOpening':
                            $dataPoint['valveOpening'] = round(rand(300, 320) / 10, 1);
                            break;
                    }
                }
                $chartData[] = $dataPoint;
            }
            $currentTimestamp += $intervalSeconds;
            $dataCount++;
        }

        // 模拟总数，实际应从数据库查询
        // 这里简单处理，如果请求的页码超出现有模拟数据，则返回空
        $simulatedTotal = floor(($endTimestamp - strtotime($params['startTime'])) / $intervalSeconds) + 1;

        if ($startIndex >= $simulatedTotal) {
            $chartData = [];
        }

        return $this->success([
            'total'     => $simulatedTotal,
            'chartData' => $chartData,
            'tableData' => $chartData, // 表格数据与图表数据相同
        ]);
    }

    /**
     * 导出设备历史数据
     * @return Response
     */
    public function historyDataExport(): Response
    {
        $params = $this->request->param();

        // 参数验证
        if (empty($params['deviceId']) || empty($params['startTime']) || empty($params['endTime']) || empty($params['dataTypes'])) {
            return $this->error('参数错误：缺少必要参数');
        }

        // 验证时间格式
        if (!strtotime($params['startTime']) || !strtotime($params['endTime'])) {
            return $this->error('时间格式错误');
        }

        // 验证时间范围
        if (strtotime($params['startTime']) >= strtotime($params['endTime'])) {
            return $this->error('开始时间必须小于结束时间');
        }

        try {
            $interval = $params['interval'] ?? '10m';
            $format   = $params['format']   ?? 'excel'; // 支持 'excel' 或 'csv'

            // 生成导出数据
            $exportData = $this->generateHistoryData($params, $interval);

            if (empty($exportData)) {
                return $this->error('没有找到符合条件的数据');
            }

            // 实例化导出服务
            $exportService = new ExportService();

            // 获取表头配置
            $headers = $exportService->getDeviceHistoryHeaders($params['dataTypes']);

            // 如果有layer参数，添加到headers中
            if (!empty($params['layer'])) {
                $headers = array_merge(['layer' => '层级'], $headers);
            }

            // 生成文件名
            $filename = sprintf(
                'device_%s_history_%s_to_%s',
                $params['deviceId'],
                date('Ymd', strtotime($params['startTime'])),
                date('Ymd', strtotime($params['endTime']))
            );

            // 根据格式导出
            if ($format === 'csv') {
                return $exportService->exportCsv($exportData, $headers, $filename);
            }

            // 智能导出：优先尝试Excel，失败则降级到CSV
            return $exportService->smartExport($exportData, $headers, $filename, 'excel');
        } catch (\Exception $e) {
            // 记录错误日志
            $this->logService->recordOperationLog(
                '导出设备历史数据失败',
                [
                    'deviceId' => $params['deviceId'],
                    'error'    => $e->getMessage(),
                ]
            );

            return $this->error('导出失败：' . $e->getMessage());
        }
    }

    /**
     * 生成历史数据（模拟数据，实际项目中应从数据库获取）
     * @param array $params 参数
     * @param string $interval 时间间隔
     * @return array
     */
    private function generateHistoryData(array $params, string $interval): array
    {
        $exportData       = [];
        $currentTimestamp = strtotime($params['startTime']);
        $endTimestamp     = strtotime($params['endTime']);

        // 根据interval计算步长（秒）
        $intervalSeconds = $this->getIntervalSeconds($interval);

        // 限制最大导出数据量（防止内存溢出）
        $maxRecords  = 50000;
        $recordCount = 0;

        while ($currentTimestamp <= $endTimestamp && $recordCount < $maxRecords) {
            $dataPoint = [
                'time' => date('Y-m-d H:i:s', $currentTimestamp),
            ];

            // 如果提供了layer参数，添加到数据中
            if (!empty($params['layer'])) {
                $dataPoint['layer'] = $params['layer'];
            }

            // 生成各种数据类型的模拟数据
            foreach ($params['dataTypes'] as $dataType) {
                $dataPoint[$dataType] = $this->generateMockData($dataType, $currentTimestamp);
            }

            $exportData[] = $dataPoint;
            $currentTimestamp += $intervalSeconds;
            $recordCount++;
        }

        return $exportData;
    }

    /**
     * 获取时间间隔秒数
     * @param string $interval 时间间隔
     * @return int
     */
    private function getIntervalSeconds(string $interval): int
    {
        return match ($interval) {
            '1m'    => 60,
            '5m'    => 5  * 60,
            '10m'   => 10 * 60,
            '30m'   => 30 * 60,
            '1h'    => 60 * 60,
            '6h'    => 6  * 60 * 60,
            '1d'    => 24 * 60 * 60,
            default => 10 * 60, // 默认10分钟
        };
    }

    /**
     * 生成模拟数据
     * @param string $dataType 数据类型
     * @param int $timestamp 时间戳
     * @return float
     */
    private function generateMockData(string $dataType, int $timestamp): float
    {
        // 添加一些基于时间的变化，使数据更真实
        $timeFactor = sin($timestamp / 3600) * 0.1 + 1; // 基于小时的周期性变化

        return match ($dataType) {
            'flow'          => round((rand(1000, 2000) / 10) * $timeFactor, 1),
            'innerPressure' => round((rand(1000, 2000) / 100) * $timeFactor, 2),
            'outerPressure' => round((rand(500, 1500) / 100) * $timeFactor, 2),
            'temperature'   => round((rand(400, 600) / 10) * $timeFactor, 1),
            'valveOpening'  => round((rand(600, 900) / 10) * $timeFactor, 1),
            default         => 0.0,
        };
    }
}
