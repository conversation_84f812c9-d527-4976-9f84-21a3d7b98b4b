<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\mqtt\MqttService;
use app\common\service\redis\RedisPoolService;
use app\tenant\service\DeviceStatusService;

use function Swoole\Coroutine\run;

use Swoole\Process;
use Swoole\Runtime;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

/**
 * 设备状态监控命令
 * 用于监听设备心跳，跟踪设备在线状态
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class DeviceStatusMonitorCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * Redis连接池服务实例
     * @var RedisPoolService|null
     */
    protected ?RedisPoolService $redisPoolService = null;

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    /**
     * Redis键前缀
     * @var string
     */
    protected string $redisKeyPrefix;

    /**
     * 设备状态过期时间（秒）
     * 如果在这段时间内没有收到心跳，则认为设备离线
     * @var int
     */
    protected int $deviceStatusExpire = 120; // 2分钟

    /**
     * 心跳消息有效期（秒）
     * 超过此时间的心跳消息将被忽略（用于过滤MQTT保留消息）
     * @var int
     */
    protected int $heartbeatValidPeriod = 300; // 5分钟

    /**
     * 监控报告间隔（秒）
     * @var int
     */
    protected int $monitoringReportInterval = 5; // 5秒

    /**
     * 上次监控报告时间
     * @var int
     */
    protected int $lastMonitoringReport = 0;

    /**
     * 是否启用连接池监控
     * @var bool
     */
    protected bool $enablePoolMonitoring = true;

    /**
     * 是否忽略MQTT保留消息
     * @var bool
     */
    protected bool $ignoreRetainedMessages = true;

    /**
     * 是否自动清理过期的保留消息
     * @var bool
     */
    protected bool $autoCleanRetainedMessages = true;

    /**
     * 待清理的MQTT主题队列
     * @var array
     */
    protected array $cleanupQueue = [];

    /**
     * 设备状态服务
     * @var DeviceStatusService
     */
    protected DeviceStatusService $deviceStatusService;

    public function __construct()
    {
        parent::__construct();

        // 合并全局配置和tenant模块配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'device_status_monitor.lock';
        // 初始化设备状态服务
        $this->deviceStatusService = DeviceStatusService::getInstance();

        // 初始化Redis连接池服务，使用动态连接池大小（根据CPU核心数自动调整）
        $this->redisPoolService = RedisPoolService::getInstance();

        // 初始化Redis键前缀
        $this->initRedisPrefix();
    }

    /**
     * 初始化Redis键前缀
     */
    protected function initRedisPrefix(): void
    {
        // 使用ThinkPHP的全局Redis前缀配置，不再手动添加前缀
        // RedisPoolService会自动处理全局前缀，这里只设置业务相关的键前缀

        // 如果配置中有定义设备前缀，则使用配置中的值
        if (isset($this->config['redis_prefix']) && !empty($this->config['redis_prefix'])) {
            $this->redisKeyPrefix = $this->config['redis_prefix'];
        } else {
            // 否则使用默认值（不包含全局前缀，由Redis连接层自动添加）
            $this->redisKeyPrefix = 'tenant:device:';
        }

        // 确保前缀以冒号结尾
        if (!empty($this->redisKeyPrefix) && !str_ends_with($this->redisKeyPrefix, ':')) {
            $this->redisKeyPrefix .= ':';
        }

        // 不再手动添加全局前缀，由RedisPoolService统一处理
        // 这样保证了与ThinkPHP缓存系统的前缀一致性
    }

    /**
     * 配置指令
     * php think tenant:deviceStatusMonitor
     * php think tenant:deviceStatusMonitor -t 10001
     * php think tenant:deviceStatusMonitor --tenant 租户编码
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:deviceStatusMonitor')
            ->setDescription('启动设备状态监控服务')
            ->addOption('tenant', 't', Option::VALUE_OPTIONAL, '指定租户ID，不指定则监听所有租户', null)
            ->addOption('enable-pool-monitor', 'm', Option::VALUE_NONE, '启用Redis连接池监控')
            ->addOption('process-retained', 'r', Option::VALUE_NONE, '处理所有MQTT保留消息（默认：忽略过期消息并自动清理）')
            ->addOption('clean-redis-data', null, Option::VALUE_NONE, '清理旧的序列化Redis数据（一次性操作）');
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个设备状态监控服务实例正在运行，进程ID：{$pid}");

                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        $config = $this->config;

        Runtime::enableCoroutine();

        // 检查是否启用连接池监控
        $this->enablePoolMonitoring = $input->getOption('enable-pool-monitor');

        if (!$this->enablePoolMonitoring) {
            $this->logInfo('Redis连接池监控已禁用');
        } else {
            // 显示连接池配置信息
            $this->logInfo('Redis连接池监控已启用');
            $this->logInfo('连接池配置: CPU核心数=' . swoole_cpu_num() . ', 连接池大小=' . min(20, max(10, swoole_cpu_num() * 5)));
        }

        // 检查保留消息处理模式
        $processRetained = $input->getOption('process-retained');

        if ($processRetained) {
            // 处理所有保留消息模式
            $this->ignoreRetainedMessages    = false;
            $this->autoCleanRetainedMessages = false;
            $this->logInfo('MQTT保留消息处理: 处理所有保留消息（包括过期消息）');
        } else {
            // 默认模式：忽略过期保留消息并自动清理
            $this->ignoreRetainedMessages    = true;
            $this->autoCleanRetainedMessages = true;
            $this->logInfo('MQTT保留消息处理: 忽略过期消息并自动清理');
        }

        // 检查是否需要清理旧的Redis数据
        if ($input->getOption('clean-redis-data')) {
            $this->logInfo('开始清理旧的序列化Redis数据...');
            $this->cleanOldRedisData();
            $this->logInfo('Redis数据清理完成，程序退出');

            return;
        }

        // 检查协程环境
        $swooleExists = class_exists('\Swoole\Coroutine');
        $this->logInfo('Swoole扩展检测: ' . ($swooleExists ? '已安装' : '未安装'));

        if ($swooleExists) {
            $this->logInfo('Swoole版本: ' . SWOOLE_VERSION);
        }

        // 在协程环境中运行MQTT客户端
        run(function() use ($input, $output, $config) {
            try {
                // 检查协程环境
                $coroutineId = \Swoole\Coroutine::getCid();
                $this->logInfo("协程环境检测: 协程ID = {$coroutineId}");

                if ($coroutineId === -1) {
                    $this->logWarning('警告: 当前不在协程环境中，Redis连接池可能无法正常工作');
                } else {
                    $this->logInfo("成功进入协程环境，协程ID: {$coroutineId}");
                }
                // 初始化MQTT服务
                $this->mqttService = MqttService::getInstance();

                if (!$this->mqttService->connect()) {
                    $this->logError('MQTT服务连接失败，失败原因请查看Log');

                    return;
                }

                // 获取租户编码参数
                $tenantCode = $input->getOption('tenant');

                // 主题名称，指定租户ID，不指定则监听所有租户
                $heartbeatTopic = $this->mqttService->buildTopic($config['topic_patterns']['device_heartbeat'], [
                    'tenant_code' => $tenantCode ?: '+',
                    'device_id'   => '+',
                ]);

                // 按协议版本组合成数组
                $topics = [
                    $heartbeatTopic => ['qos' => 0, 'no_local' => true, 'retain' => false],
                ];

                // 订阅主题
                if (!$this->mqttService->subscribe($topics)) {
                    $this->logError('订阅主题失败');

                    return;
                }

                if ($tenantCode) {
                    $this->logInfo("开始监听租户【{$tenantCode}】的设备心跳主题[{$heartbeatTopic}]");
                } else {
                    $this->logInfo("开始监听所有租户的设备心跳主题[{$heartbeatTopic}]");
                }

                // 使用 Swoole 的信号处理
                $signalHandler = function($signal) {
                    static $isShuttingDown = false; // 用于确保清理逻辑只执行一次

                    if ($isShuttingDown) {
                        $this->logInfo('服务正在关闭中，尝试强制停止事件循环...');

                        // 如果已经在关闭过程中，再次收到信号，尝试停止Swoole事件循环
                        if (class_exists('\Swoole\Event')) {
                            \Swoole\Event::exit();
                        }

                        return;
                    }
                    $isShuttingDown = true;

                    $this->running = false;
                    $this->logInfo('收到信号 ' . $signal . '，准备停止服务');

                    // 尝试中断MQTT接收操作
                    if (isset($this->mqttService) && $this->mqttService->isConnected()) {
                        $this->logInfo('尝试断开MQTT连接以中断接收...');
                        $this->mqttService->disconnect(); // 主动断开，使receive()不再阻塞
                    }

                    // 清理资源的逻辑将主要放在主循环退出后执行
                    // 这里可以做一些快速的标记或预处理
                };

                Process::signal(SIGTERM, $signalHandler);
                Process::signal(SIGINT, $signalHandler);

                // 监听循环
                while ($this->running) {
                    // 定期输出监控报告
                    $this->outputMonitoringReport();

                    // 预热Redis连接池（仅在第一次监控报告后执行一次）
                    static $poolWarmedUp = false;

                    if (!$poolWarmedUp && $this->enablePoolMonitoring) {
                        try {
                            // 执行一个简单的Redis操作来预热连接池
                            $testKey = $this->redisKeyPrefix . 'warmup:' . time();
                            $this->redisPoolService->set($testKey, 'warmup', 1); // 1秒后过期
                            $this->logInfo('Redis连接池预热完成');
                            $poolWarmedUp = true;
                        } catch (\Exception $e) {
                            $this->logError('Redis连接池预热失败: ' . $e->getMessage());
                        }
                    }

                    // 检查连接状态并自动重连
                    if (!$this->mqttService->isConnected()) {
                        $this->logWarning('检测到MQTT连接断开，尝试重新连接...');

                        if (!$this->mqttService->connect()) {
                            $this->logError('MQTT重连失败，5秒后重试');
                            \Swoole\Coroutine::sleep(5);
                            continue;
                        }
                        $this->logInfo('MQTT重连成功');

                        // 重新订阅主题
                        if (!$this->mqttService->subscribe($topics)) {
                            $this->logError('重新订阅主题失败');
                            continue;
                        }
                    }

                    // 接收消息
                    $result = $this->mqttService->receive();

                    if (!$result) {
                        // 发送心跳包保持连接
                        if (!$this->mqttService->ping()) {
                            $this->logError('发送心跳包失败，标记连接为断开状态');
                            $this->mqttService->disconnect();
                        }
                        continue;
                    }

                    // 说明是keepalive心跳
                    if ($result === true) {
                        continue;
                    }

                    // 调试模式下打印消息到屏幕
                    if ($config['debug']) {
                        $this->logDebug('收到设备心跳消息: ' . (!is_array($result) ? $result : ''));

                        if (is_array($result)) {
                            dump($result);
                        }
                    }

                    try {
                        // 解析消息
                        $message = json_decode($result['message'] ?? '', true);

                        if (!$message) {
                            continue;
                        }

                        // 检查是否为保留消息
                        $isRetained = isset($result['retain']) && $result['retain'] === 1;

                        // 处理设备心跳消息
                        $this->processDeviceHeartbeat($result['topic'], $message, $isRetained);

                        // 执行清理任务（如果有的话）
                        $this->executeCleanupTasks();
                    } catch (\Exception $e) {
                        $this->logError('处理设备心跳消息异常: ' . $e->getMessage());
                    }
                }

                // 优雅退出
                $this->logWarning('设备状态监控服务已停止');

                // 确保清理逻辑只执行一次
                static $finalCleanupDone = false;

                if (!$finalCleanupDone) {
                    $this->logInfo('开始最终资源清理...');

                    try {
                        // 关闭Redis连接池
                        if ($this->redisPoolService !== null) {
                            $this->redisPoolService->close();
                            $this->logInfo('Redis连接池已关闭');

                            // 输出最终的连接池统计信息
                            try {
                                $finalStats = $this->redisPoolService->getStats();
                                $this->logInfo('最终连接池统计: 总操作=' . $finalStats['operation_stats']['total_operations'] .
                                             ', 成功率=' . $finalStats['success_rate'] . '%');
                            } catch (\Exception $e) {
                                $this->logDebug('获取最终统计信息失败: ' . $e->getMessage());
                            }
                        }

                        // 断开MQTT连接 (如果信号处理器中没有断开)
                        if (isset($this->mqttService) && $this->mqttService->isConnected()) {
                            $this->mqttService->disconnect();
                            $this->logInfo('MQTT连接已断开 (最终清理)');
                        }
                    } catch (\Throwable $e) {
                        $this->logError('最终资源清理异常: ' . $e->getMessage());
                    }

                    // 删除进程锁
                    if (file_exists($this->lockFile)) {
                        unlink($this->lockFile);
                        $this->logInfo('进程锁文件已删除 (最终清理)');
                    }
                    $finalCleanupDone = true;
                    $this->logInfo('最终资源清理完成');
                } else {
                    $this->logInfo('最终资源清理已执行过，跳过。');
                }
            } catch (\Throwable $e) {
                $this->logError('设备状态监控服务异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

                // 删除进程锁
                if (file_exists($this->lockFile)) {
                    unlink($this->lockFile);
                }
            }
        });
    }

    /**
     * 处理设备心跳消息
     * @param string $topic 主题
     * @param array $message 消息内容
     * @param bool $isRetained 是否为保留消息
     */
    protected function processDeviceHeartbeat(string $topic, array $message, bool $isRetained = false): void
    {
        try {
            // 如果配置为忽略保留消息，且当前消息是保留消息
            if ($this->ignoreRetainedMessages && $isRetained) {
                // 检查是否需要清理过期的保留消息
                if ($this->autoCleanRetainedMessages) {
                    // 解析消息以获取时间戳
                    $timestamp   = $message['timestamp'] ?? time();
                    $currentTime = time();
                    $messageAge  = $currentTime - $timestamp;

                    if ($messageAge > $this->heartbeatValidPeriod) {
                        $this->logDebug("发现过期MQTT保留消息，添加到清理队列: 主题[{$topic}] 已过期{$messageAge}秒");
                        $this->addCleanupTask($topic);
                    } else {
                        $this->logDebug("忽略MQTT保留消息（未过期）: 主题[{$topic}]");
                    }
                } else {
                    $this->logDebug("忽略MQTT保留消息: 主题[{$topic}]");
                }

                return;
            }

            // 优先从消息中获取租户编码和设备ID
            $tenantCode = $message['tenantCode'] ?? null;
            $deviceId   = $message['deviceId']   ?? 0;

            // 如果消息中没有租户编码和设备ID，则尝试从主题中提取
            if (!$tenantCode || !$deviceId) {
                // 从配置文件中获取设备心跳主题模式
                $topicPattern = $this->config['topic_patterns']['device_heartbeat'] ?? '';

                if (!empty($topicPattern)) {
                    // 使用静态缓存存储编译后的正则表达式，避免重复编译
                    static $regexPattern = null;

                    if ($regexPattern === null) {
                        // 将主题模式转换为正则表达式模式
                        $regexPattern = '#' . str_replace(
                            ['{prefix}', '{tenant_code}', '{device_id}', '/'],
                            ['.*?', '([^/]+)', '([^/]+)', '\\/'],
                            $topicPattern
                        ) . '#';
                    }

                    $matches = [];

                    if (preg_match($regexPattern, $topic, $matches)) {
                        $tenantCode = $matches[1] ?? null;
                        $deviceId   = $matches[2] ?? null;
                    }
                }
            }

            // 如果仍然无法获取租户编码和设备ID，则记录警告并返回
            if (!$tenantCode || !$deviceId) {
                $this->logWarning("无法获取租户编码和设备ID，主题：{$topic}");

                return;
            }

            $status    = $message['status']    ?? 'offline';
            $timestamp = $message['timestamp'] ?? time();

            $this->logDebug("收到设备心跳消息: 租户[{$tenantCode}] 设备[{$deviceId}] 状态[{$status}] 消息时间[" . date('Y-m-d H:i:s', $timestamp) . ']');

            // 根据状态处理
            if ($status === 'online' || $status === 'alive') {
                // 设备上线或心跳
                $this->updateDeviceStatus((string)$tenantCode, (int)$deviceId, $status, (int)$timestamp);
            } elseif ($status === 'offline') {
                // 设备离线
                $this->setDeviceOffline((string)$tenantCode, (int)$deviceId, (int)$timestamp);
            } else {
                // 未知状态
                $this->logWarning("收到未知状态的设备心跳消息: 租户[{$tenantCode}] 设备[{$deviceId}] 状态[{$status}]");
            }
        } catch (\Throwable $e) {
            $this->logError('处理设备心跳消息异常: ' . $e->getMessage());
        }
    }

    /**
     * 更新设备状态
     * @param string $tenantCode 租户编码
     * @param int $deviceId 设备ID
     * @param string $status 状态
     * @param int $timestamp 时间戳
     */
    protected function updateDeviceStatus(string $tenantCode, int $deviceId, string $status, int $timestamp): void
    {
        try {
            // 构建Redis键
            $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            if ($status === 'online' || $status === 'alive') {
                // 设备上线或心跳
                $statusData = [
                    'status'        => $status,
                    'lastHeartbeat' => $timestamp,
                    'tenantCode'    => $tenantCode,
                    'deviceId'      => $deviceId,
                    'online'        => true,
                ];

                // 使用事务确保原子性
                $results = $this->redisPoolService->transaction(function($redis) use ($statusKey, $onlineKey, $statusData, $deviceId) {
                    // 设置设备状态，并设置过期时间
                    $redis->hMset($statusKey, $statusData);
                    $redis->expire($statusKey, $this->deviceStatusExpire);

                    // 将设备添加到在线集合
                    $redis->sAdd($onlineKey, $deviceId);
                    $redis->expire($onlineKey, $this->deviceStatusExpire);
                });

                // 验证事务执行结果
                if (!$results || in_array(false, $results, true)) {
                    throw new \Exception('Redis transaction failed');
                }

                if ($status === 'online') {
                    $this->logInfo("设备上线: 租户[{$tenantCode}] 设备[{$deviceId}]");
                } else {
                    $this->logDebug("设备心跳: 租户[{$tenantCode}] 设备[{$deviceId}]");
                }
            } elseif ($status === 'offline') {
                // 设备离线
                $results = $this->redisPoolService->transaction(function($redis) use ($statusKey, $onlineKey, $deviceId, $timestamp) {
                    // 从在线集合中移除设备
                    $redis->sRem($onlineKey, $deviceId);

                    // 更新设备状态为离线
                    $redis->hSet($statusKey, 'status', 'offline');
                    $redis->hSet($statusKey, 'lastHeartbeat', $timestamp);
                    $redis->hSet($statusKey, 'online', false);

                    // 设置较短的过期时间，让Redis自动清理
                    $redis->expire($statusKey, 86400); // 24小时后自动清理
                });

                // 验证事务执行结果
                if (!$results || in_array(false, $results, true)) {
                    throw new \Exception('Redis transaction failed');
                }

                $this->logInfo("设备离线: 租户[{$tenantCode}] 设备[{$deviceId}]");
            }
        } catch (\Exception $e) {
            $this->logError('更新设备状态异常: ' . $e->getMessage());
            // 记录详细的错误信息
            $this->logError('Redis操作失败详情: ' . json_encode([
                'tenantCode' => $tenantCode,
                'deviceId'   => $deviceId,
                'status'     => $status,
                'timestamp'  => $timestamp,
                'error'      => $e->getMessage(),
            ]));
        }
    }

    /**
     * 获取租户的在线设备
     * @param string $tenantCode 租户编码
     * @return array 在线设备ID数组
     */
    protected function getTenantOnlineDevices(string $tenantCode): array
    {
        try {
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            return $this->redisPoolService->sMembers($onlineKey);
        } catch (\Exception $e) {
            $this->logError('获取在线设备异常: ' . $e->getMessage());

            return [];
        }
    }

    /**
     * 获取设备状态
     * @param int $deviceId 设备ID
     * @param string $tenantCode 租户编码
     * @return array|null 设备状态信息
     */
    protected function getDeviceStatus(int $deviceId, string $tenantCode): ?array
    {
        try {
            $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";
            $status    = $this->redisPoolService->hGetAll($statusKey);

            return !empty($status) ? $status : null;
        } catch (\Exception $e) {
            $this->logError('获取设备状态异常: ' . $e->getMessage());

            return null;
        }
    }

    /**
     * 设置设备离线
     * @param string $tenantCode 租户编码
     * @param int $deviceId 设备ID
     * @param int $timestamp 时间戳
     */
    protected function setDeviceOffline(string $tenantCode, int $deviceId, int $timestamp): void
    {
        try {
            // 构建Redis键
            $statusKey = $this->redisKeyPrefix . "status:{$tenantCode}:{$deviceId}";
            $onlineKey = $this->redisKeyPrefix . "online:{$tenantCode}";

            // 使用管道批量操作
            $this->redisPoolService->pipeline(function($pipeline) use ($statusKey, $onlineKey, $deviceId, $timestamp) {
                // 从在线集合中移除设备
                $pipeline->sRem($onlineKey, $deviceId);

                // 更新设备状态为离线
                $pipeline->hSet($statusKey, 'status', 'offline');
                $pipeline->hSet($statusKey, 'lastHeartbeat', $timestamp);

                // 设置较短的过期时间，让Redis自动清理
                $pipeline->expire($statusKey, 86400); // 24小时后自动清理
            });

            $this->logInfo("设备离线: 租户[{$tenantCode}] 设备[{$deviceId}]");
        } catch (\Exception $e) {
            $this->logError('设置设备离线异常: ' . $e->getMessage());
        }
    }

    /**
     * 输出监控报告
     */
    protected function outputMonitoringReport(): void
    {
        // 如果禁用了连接池监控，直接返回
        if (!$this->enablePoolMonitoring) {
            return;
        }

        $currentTime = time();

        // 检查是否需要输出监控报告
        if ($currentTime - $this->lastMonitoringReport >= $this->monitoringReportInterval) {
            try {
                $report = $this->redisPoolService->getMonitoringReport();
                $this->logInfo("Redis连接池监控报告:\n" . $report);

                $this->lastMonitoringReport = $currentTime;
            } catch (\Exception $e) {
                $this->logError('生成监控报告异常: ' . $e->getMessage());
            }
        }
    }

    /**
     * 获取Redis连接池统计信息
     * @return array
     */
    public function getRedisStats(): array
    {
        try {
            return $this->redisPoolService->getStats();
        } catch (\Exception $e) {
            $this->logError('获取Redis统计信息异常: ' . $e->getMessage());

            return [];
        }
    }

    /**
     * 重置Redis统计信息
     */
    public function resetRedisStats(): void
    {
        try {
            $this->redisPoolService->resetStats();
            $this->logInfo('Redis统计信息已重置');
        } catch (\Exception $e) {
            $this->logError('重置Redis统计信息异常: ' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用Redis监控
     * @param bool $enable
     */
    public function setRedisMonitoring(bool $enable): void
    {
        try {
            $this->redisPoolService->setMonitoring($enable);
            $status = $enable ? '启用' : '禁用';
            $this->logInfo("Redis监控已{$status}");
        } catch (\Exception $e) {
            $this->logError('设置Redis监控状态异常: ' . $e->getMessage());
        }
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;
                break;
        }
    }

    /**
     * 添加清理任务到队列
     * @param string $topic
     */
    protected function addCleanupTask(string $topic): void
    {
        if (!in_array($topic, $this->cleanupQueue)) {
            $this->cleanupQueue[] = $topic;
            $this->logDebug("添加清理任务到队列: 主题[{$topic}]");
        }
    }

    /**
     * 执行清理任务
     * 使用MqttService的批量清理方法
     */
    protected function executeCleanupTasks(): void
    {
        if (empty($this->cleanupQueue)) {
            return;
        }

        try {
            // 使用MqttService的批量清理方法
            $result = $this->mqttService->clearRetainedMessages($this->cleanupQueue);

            // 记录清理结果
            if (!empty($result['success'])) {
                $this->logInfo('成功清理过期保留消息: ' . count($result['success']) . '个');

                foreach ($result['success'] as $topic) {
                    $this->logDebug("已清理: 主题[{$topic}]");
                }
            }

            if (!empty($result['failed'])) {
                $this->logError('清理保留消息失败: ' . count($result['failed']) . '个');

                foreach ($result['failed'] as $topic) {
                    $this->logError("清理失败: 主题[{$topic}]");
                }
            }

            // 清空队列
            $this->cleanupQueue = [];
        } catch (\Exception $e) {
            $this->logError('执行清理任务异常: ' . $e->getMessage());
        }
    }

    /**
     * 清理旧的序列化Redis数据
     * 将序列化格式的数据转换为新的非序列化格式
     */
    protected function cleanOldRedisData(): void
    {
        try {
            // 获取所有租户的Redis键模式
            $patterns = [
                $this->redisKeyPrefix . 'status:*',
                $this->redisKeyPrefix . 'online:*',
            ];

            foreach ($patterns as $pattern) {
                $this->logInfo("清理模式: {$pattern}");

                // 获取匹配的键
                $keys = $this->redisPoolService->execute(function($redis) use ($pattern) {
                    return $redis->keys($pattern);
                });

                if (empty($keys)) {
                    $this->logInfo("没有找到匹配的键: {$pattern}");
                    continue;
                }

                $this->logInfo('找到 ' . count($keys) . ' 个键需要清理');

                foreach ($keys as $key) {
                    $this->cleanRedisKey($key);
                }
            }
        } catch (\Exception $e) {
            $this->logError('清理Redis数据异常: ' . $e->getMessage());
        }
    }

    /**
     * 清理单个Redis键的序列化数据
     * @param string $key
     */
    protected function cleanRedisKey(string $key): void
    {
        try {
            if (strpos($key, ':status:') !== false) {
                // 处理设备状态键（Hash类型）
                $this->cleanStatusKey($key);
            } elseif (strpos($key, ':online:') !== false) {
                // 处理在线设备集合键（Set类型）
                $this->cleanOnlineKey($key);
            }
        } catch (\Exception $e) {
            $this->logError("清理键 {$key} 异常: " . $e->getMessage());
        }
    }

    /**
     * 清理设备状态键的序列化数据
     * @param string $key
     */
    protected function cleanStatusKey(string $key): void
    {
        $statusData = $this->redisPoolService->hGetAll($key);

        if (empty($statusData)) {
            return;
        }

        $needsUpdate = false;
        $newData     = [];

        foreach ($statusData as $field => $value) {
            if (is_string($value) && (strpos($value, 's:') === 0 || strpos($value, 'i:') === 0 || strpos($value, 'b:') === 0)) {
                // 这是序列化的数据，需要反序列化
                $unserializedValue = @unserialize($value);

                if ($unserializedValue !== false || $value === 'b:0;') {
                    $newData[$field] = $unserializedValue;
                    $needsUpdate     = true;
                } else {
                    $newData[$field] = $value;
                }
            } else {
                $newData[$field] = $value;
            }
        }

        if ($needsUpdate) {
            // 删除旧键并重新创建
            $this->redisPoolService->execute(function($redis) use ($key, $newData) {
                $redis->del($key);
                $redis->hMset($key, $newData);
                $redis->expire($key, $this->deviceStatusExpire);
            });

            $this->logInfo("已清理状态键: {$key}");
        }
    }

    /**
     * 清理在线设备集合键的序列化数据
     * @param string $key
     */
    protected function cleanOnlineKey(string $key): void
    {
        $members = $this->redisPoolService->sMembers($key);

        if (empty($members)) {
            return;
        }

        $needsUpdate = false;
        $newMembers  = [];

        foreach ($members as $member) {
            if (is_string($member) && preg_match('/^i:(\d+);$/', $member, $matches)) {
                // 这是序列化的设备ID
                $newMembers[] = (int)$matches[1];
                $needsUpdate  = true;
            } else {
                $newMembers[] = $member;
            }
        }

        if ($needsUpdate) {
            // 删除旧键并重新创建
            $this->redisPoolService->execute(function($redis) use ($key, $newMembers) {
                $redis->del($key);

                if (!empty($newMembers)) {
                    $redis->sAdd($key, ...$newMembers);
                    $redis->expire($key, $this->deviceStatusExpire);
                }
            });

            $this->logInfo("已清理在线键: {$key}，设备数: " . count($newMembers));
        }
    }
}
