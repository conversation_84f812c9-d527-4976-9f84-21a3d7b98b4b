<?php
declare(strict_types = 1);

namespace app\tenant\command\test;

use app\common\service\redis\RedisPoolService;
use Swoole\Coroutine;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;

/**
 * Redis监控测试命令
 * @package app\tenant\command
 */
class RedisMonitorTestCommand extends Command
{
    /**
     * Redis服务实例
     * @var RedisPoolService|null
     */
    protected ?RedisPoolService $redisManager = null;

    /**
     * 配置指令
     */
    protected function configure(): void
    {
        $this->setName('tenant:redisMonitorTest')
            ->setDescription('测试Redis连接池监控功能');
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        try {
            // 初始化Redis服务
            $this->redisManager = RedisPoolService::getInstance();

            // 启用监控
            $this->redisManager->setMonitoring(true);

            // 在协程环境中运行测试
            Coroutine\run(function() use ($output) {
                try {
                    // 执行一些Redis操作来测试连接池
                    $this->testRedisOperations();

                    // 获取监控报告
                    $report = $this->redisManager->getMonitoringReport();
                    $output->writeln("Redis连接池监控报告:\n" . $report);

                    // 获取统计信息
                    $stats = $this->redisManager->getStats();
                    $output->writeln("\nRedis连接池统计信息:");
                    $output->writeln(json_encode($stats, JSON_PRETTY_PRINT));

                    // 重置统计信息
                    $this->redisManager->resetStats();
                    $output->writeln("\nRedis统计信息已重置");
                } catch (\Throwable $e) {
                    $output->writeln('<error>测试过程中发生错误: ' . $e->getMessage() . '</error>');
                    Log::error('Redis监控测试异常: ' . $e->getMessage());
                } finally {
                    // 关闭Redis服务
                    if ($this->redisManager !== null) {
                        $this->redisManager->close();
                    }
                }
            });
        } catch (\Throwable $e) {
            $output->writeln('<error>命令执行失败: ' . $e->getMessage() . '</error>');
            Log::error('Redis监控测试命令异常: ' . $e->getMessage());
        }
    }

    /**
     * 测试Redis操作
     */
    protected function testRedisOperations(): void
    {
        // 测试基本操作
        $this->redisManager->set('test_key', 'test_value');
        $value = $this->redisManager->get('test_key');

        // 测试事务
        $this->redisManager->transaction(function($redis) {
            $redis->set('transaction_key1', 'value1');
            $redis->set('transaction_key2', 'value2');
        });

        // 测试管道
        $this->redisManager->pipeline(function($pipeline) {
            $pipeline->set('pipeline_key1', 'value1');
            $pipeline->set('pipeline_key2', 'value2');
        });

        // 清理测试数据
        $this->redisManager->del('test_key', 'transaction_key1', 'transaction_key2', 'pipeline_key1', 'pipeline_key2');
    }
}
