<?php
declare(strict_types = 1);

namespace app\tenant\command\test;

use app\common\service\mqtt\MqttService;
use app\tenant\command\BaseCommand;
use Swoole\Coroutine;

use function Swoole\Coroutine\run;

use Swoole\Runtime;
use think\console\Input;
use think\console\Output;

/**
 * 设备模拟器命令
 * 用于模拟多个设备的在线状态和心跳发送
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class DeviceSimulatorCommand extends BaseCommand
{
    /**
     * MQTT配置
     * @var array
     */
    private array $config = [];

    /**
     * 进程锁文件路径
     * @var string
     */
    private string $lockFile = '';

    /**
     * MQTT服务实例
     * @var MqttService
     */
    protected MqttService $mqttService;

    /**
     * 是否继续运行
     * @var bool
     */
    protected bool $running = true;

    /**
     * 在线设备列表
     * @var array
     */
    protected array $onlineDevices = [];

    /**
     * 所有可用设备ID列表
     * @var array
     */
    protected array $allDeviceIds = [];

    /**
     * 心跳间隔（秒）
     * @var int
     */
    protected int $heartbeatInterval = 30;

    public function __construct()
    {
        parent::__construct();

        // 获取tenant模块的mqtt配置
        $this->config = getAppConfig('mqtt', 'tenant');
        // 初始化进程锁文件路径
        $this->lockFile = runtime_path() . 'device_simulator.lock';
        // 初始化设备ID列表
        $this->allDeviceIds = range(10001, 10600);
    }

    /**
     * 配置指令
     * php think tenant:deviceSimulator
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('tenant:deviceSimulator')
            ->setDescription('启动设备模拟器服务');
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        // 检查是否已有实例在运行
        if (file_exists($this->lockFile)) {
            $pid = file_get_contents($this->lockFile);

            if ($pid && posix_kill((int)$pid, 0)) {
                $this->logError("已有一个设备模拟器实例正在运行，进程ID：{$pid}");

                return;
            }

            // 如果进程不存在，删除锁文件
            unlink($this->lockFile);
        }

        // 创建进程锁
        file_put_contents($this->lockFile, getmypid());

        Runtime::enableCoroutine();

        // 在协程环境中运行设备模拟器
        run(function() {
            // 初始化MQTT服务
            $this->mqttService = MqttService::getInstance();

            if (!$this->mqttService->connect()) {
                $this->logError('MQTT服务连接失败');

                return;
            }

            // 注册信号处理
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);

            // 创建信号处理协程
            Coroutine::create(function() {
                while ($this->running) {
                    pcntl_signal_dispatch();
                    Coroutine::sleep(0.1); // 每100ms检查一次信号
                }
            });

            // 随机选择200个设备
            $selectedDevices = array_rand(array_flip($this->allDeviceIds), 200);

            // 为每个设备创建一个协程
            foreach ($selectedDevices as $deviceId) {
                Coroutine::create(function() use ($deviceId) {
                    $this->simulateDevice($deviceId);
                });
            }

            // 主循环
            while ($this->running) {
                Coroutine::sleep(1);
            }

            // 优雅退出
            $this->logWarning('设备模拟器服务已停止');

            // 删除进程锁
            if (file_exists($this->lockFile)) {
                unlink($this->lockFile);
            }
        });
    }

    /**
     * 模拟单个设备行为
     * @param int $deviceId
     */
    protected function simulateDevice(int $deviceId): void
    {
        while ($this->running) {
            try {
                // 随机决定设备是否上线
                $onlineTime  = random_int(60, 300); // 在线时间1-5分钟
                $offlineTime = random_int(30, 180); // 离线时间30秒-3分钟

                // 设备上线前设置遗嘱消息
                $this->setLastWillMessage($deviceId);

                // 设备上线
                $this->onlineDevices[$deviceId] = true;

                if ($this->sendDeviceStatus($deviceId, 'online')) {
                    $this->logInfo("设备 {$deviceId} 上线");
                } else {
                    $this->logWarning("设备 {$deviceId} 上线状态发送失败");
                }

                // 在线期间发送心跳
                $startTime = time();

                while ($this->running && (time() - $startTime) < $onlineTime) {
                    if ($this->sendHeartbeat($deviceId)) {
                        $this->logInfo("设备 {$deviceId} 发送心跳成功");
                    } else {
                        $this->logWarning("设备 {$deviceId} 发送心跳失败");
                    }
                    Coroutine::sleep($this->heartbeatInterval);
                }

                // 设备正常离线
                unset($this->onlineDevices[$deviceId]);

                if ($this->sendDeviceStatus($deviceId, 'offline')) {
                    $this->logInfo("设备 {$deviceId} 正常离线");
                } else {
                    $this->logWarning("设备 {$deviceId} 离线状态发送失败");
                }

                // 等待一段时间后重新上线
                Coroutine::sleep($offlineTime);
            } catch (\Exception $e) {
                $this->logError("设备 {$deviceId} 模拟异常: " . $e->getMessage());
                Coroutine::sleep(5); // 发生异常时等待5秒后重试
            }
        }
    }

    /**
     * 发送设备心跳
     * @param int $deviceId
     * @return bool
     */
    protected function sendHeartbeat(int $deviceId): bool
    {
        try {
            // 构建心跳主题
            $heartbeatTopic = "helio/tenant/shengli/device/{$deviceId}/heartbeat";

            // 构建心跳数据
            $heartbeatData = [
                'deviceId'   => $deviceId,
                'tenantCode' => 'shengli',
                'timestamp'  => time(),
                'status'     => 'alive', // 心跳状态为alive
            ];

            // 发送心跳消息（QoS=0，retain=false）
            return $this->mqttService->publish(
                $heartbeatTopic,
                json_encode($heartbeatData, JSON_UNESCAPED_UNICODE),
                0,  // QoS=0
                0,  // dup=0
                0   // retain=false
            );
        } catch (\Exception $e) {
            $this->logError('发送心跳消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 发送设备状态消息
     * @param int $deviceId
     * @param string $status 状态：online/offline
     * @return bool
     */
    protected function sendDeviceStatus(int $deviceId, string $status): bool
    {
        try {
            // 构建状态主题
            $statusTopic = "helio/tenant/shengli/device/{$deviceId}/heartbeat";

            // 构建状态数据
            $statusData = [
                'deviceId'   => $deviceId,
                'tenantCode' => 'shengli',
                'timestamp'  => time(),
                'status'     => $status, // online或offline
            ];

            // 根据状态类型设置不同的QoS和retain值
            if ($status === 'online' || $status === 'offline') {
                // 上线和离线消息使用QoS=1，retain=true
                return $this->mqttService->publish(
                    $statusTopic,
                    json_encode($statusData, JSON_UNESCAPED_UNICODE),
                    1,  // QoS=1
                    0,  // dup=0
                    1   // retain=true
                );
            }

            // 心跳消息使用QoS=0，retain=false
            return $this->mqttService->publish(
                $statusTopic,
                json_encode($statusData, JSON_UNESCAPED_UNICODE),
                0,  // QoS=0
                0,  // dup=0
                0   // retain=false
            );
        } catch (\Exception $e) {
            $this->logError('发送设备状态消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 设置设备遗嘱消息
     * @param int $deviceId
     * @return bool
     */
    protected function setLastWillMessage(int $deviceId): bool
    {
        try {
            // 在实际环境中，遗嘱消息应该在MQTT连接时设置
            // 这里我们只是模拟记录一下，实际上无法在连接后设置遗嘱消息

            // 构建遗嘱主题
            $willTopic = "helio/tenant/shengli/device/{$deviceId}/heartbeat";

            // 构建遗嘱数据
            $willData = [
                'deviceId'   => $deviceId,
                'tenantCode' => 'shengli',
                'timestamp'  => time(),
                'status'     => 'offline', // 遗嘱消息状态为offline
                'reason'     => '设备异常断开连接',
            ];

            $this->logInfo("设备 {$deviceId} 设置遗嘱消息: Topic={$willTopic}, 内容=" . json_encode($willData, JSON_UNESCAPED_UNICODE));

            // 注意：在实际环境中，遗嘱消息应该在MQTT连接时通过连接选项设置
            // 这里我们只是模拟记录，返回true表示模拟成功
            return true;
        } catch (\Exception $e) {
            $this->logError('设置设备遗嘱消息异常: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * 处理指定信号
     * @param int $signal
     */
    public function handleSignal(int $signal): void
    {
        switch ($signal) {
            case SIGTERM:
            case SIGINT:
                $this->running = false;
                break;
        }
    }
}
