<?php
declare(strict_types = 1);

namespace app\tenant\command;

use app\common\service\RedisManager;
use Swoole\Coroutine;
use Swoole\Runtime;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * Redis连接池监控测试命令
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\command
 */
class RedisMonitorTestCommand extends Command
{
    /**
     * Redis管理器实例
     * @var RedisManager|null
     */
    protected ?RedisManager $redisManager = null;

    /**
     * 配置指令
     */
    protected function configure(): void
    {
        $this->setName('tenant:redisMonitorTest')
            ->setDescription('Redis连接池监控测试');
    }

    /**
     * 执行指令
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output): void
    {
        Runtime::enableCoroutine();

        run(function() use ($output) {
            $this->redisManager = RedisManager::getInstance();

            $output->writeln('=== Redis连接池监控测试开始 ===');

            // 测试1: 基本操作测试
            $this->testBasicOperations($output);

            // 测试2: 并发操作测试
            $this->testConcurrentOperations($output);

            // 测试3: 事务操作测试
            $this->testTransactionOperations($output);

            // 测试4: 管道操作测试
            $this->testPipelineOperations($output);

            // 测试5: 错误处理测试
            $this->testErrorHandling($output);

            // 输出最终监控报告
            $this->outputFinalReport($output);

            $output->writeln('=== Redis连接池监控测试完成 ===');
        });
    }

    /**
     * 测试基本操作
     * @param Output $output
     */
    protected function testBasicOperations(Output $output): void
    {
        $output->writeln("\n--- 测试1: 基本操作 ---");

        try {
            // 设置和获取操作
            for ($i = 1; $i <= 10; $i++) {
                $key   = "test:basic:{$i}";
                $value = "value_{$i}";

                $this->redisManager->set($key, $value, 60);
                $result = $this->redisManager->get($key);

                if ($result === $value) {
                    $output->writeln("✓ 基本操作 {$i}/10 成功");
                } else {
                    $output->writeln("✗ 基本操作 {$i}/10 失败");
                }

                // 清理
                $this->redisManager->del($key);

                Coroutine::sleep(0.1);
            }
        } catch (\Exception $e) {
            $output->writeln('✗ 基本操作测试异常: ' . $e->getMessage());
        }
    }

    /**
     * 测试并发操作
     * @param Output $output
     */
    protected function testConcurrentOperations(Output $output): void
    {
        $output->writeln("\n--- 测试2: 并发操作 ---");

        $concurrency  = 20;
        $operations   = 5;
        $successCount = 0;

        $channels = [];

        for ($i = 0; $i < $concurrency; $i++) {
            $channels[] = new \Swoole\Coroutine\Channel(1);
        }

        // 启动并发协程
        for ($i = 0; $i < $concurrency; $i++) {
            go(function() use ($i, $operations, $channels) {
                $localSuccess = 0;

                try {
                    for ($j = 0; $j < $operations; $j++) {
                        $key   = "test:concurrent:{$i}:{$j}";
                        $value = "value_{$i}_{$j}";

                        $this->redisManager->set($key, $value, 60);
                        $result = $this->redisManager->get($key);

                        if ($result === $value) {
                            $localSuccess++;
                        }

                        $this->redisManager->del($key);

                        Coroutine::sleep(0.01);
                    }
                } catch (\Exception $e) {
                    // 忽略异常，继续测试
                }

                $channels[$i]->push($localSuccess);
            });
        }

        // 收集结果
        for ($i = 0; $i < $concurrency; $i++) {
            $successCount += $channels[$i]->pop();
        }

        $totalOperations = $concurrency * $operations;
        $successRate     = round(($successCount / $totalOperations) * 100, 2);

        $output->writeln("并发操作结果: {$successCount}/{$totalOperations} 成功率: {$successRate}%");
    }

    /**
     * 测试事务操作
     * @param Output $output
     */
    protected function testTransactionOperations(Output $output): void
    {
        $output->writeln("\n--- 测试3: 事务操作 ---");

        try {
            for ($i = 1; $i <= 5; $i++) {
                $results = $this->redisManager->transaction(function($redis) use ($i) {
                    $redis->set("test:trans:key1:{$i}", "value1_{$i}");
                    $redis->set("test:trans:key2:{$i}", "value2_{$i}");
                    $redis->incr("test:trans:counter:{$i}");
                });

                if ($results && !in_array(false, $results, true)) {
                    $output->writeln("✓ 事务操作 {$i}/5 成功");
                } else {
                    $output->writeln("✗ 事务操作 {$i}/5 失败");
                }

                // 清理
                $this->redisManager->del([
                    "test:trans:key1:{$i}",
                    "test:trans:key2:{$i}",
                    "test:trans:counter:{$i}",
                ]);

                Coroutine::sleep(0.1);
            }
        } catch (\Exception $e) {
            $output->writeln('✗ 事务操作测试异常: ' . $e->getMessage());
        }
    }

    /**
     * 测试管道操作
     * @param Output $output
     */
    protected function testPipelineOperations(Output $output): void
    {
        $output->writeln("\n--- 测试4: 管道操作 ---");

        try {
            for ($i = 1; $i <= 5; $i++) {
                $results = $this->redisManager->pipeline(function($pipeline) use ($i) {
                    $pipeline->set("test:pipe:key1:{$i}", "value1_{$i}");
                    $pipeline->set("test:pipe:key2:{$i}", "value2_{$i}");
                    $pipeline->get("test:pipe:key1:{$i}");
                    $pipeline->get("test:pipe:key2:{$i}");
                });

                if ($results && count($results) === 4) {
                    $output->writeln("✓ 管道操作 {$i}/5 成功");
                } else {
                    $output->writeln("✗ 管道操作 {$i}/5 失败");
                }

                // 清理
                $this->redisManager->del([
                    "test:pipe:key1:{$i}",
                    "test:pipe:key2:{$i}",
                ]);

                Coroutine::sleep(0.1);
            }
        } catch (\Exception $e) {
            $output->writeln('✗ 管道操作测试异常: ' . $e->getMessage());
        }
    }

    /**
     * 测试错误处理
     * @param Output $output
     */
    protected function testErrorHandling(Output $output): void
    {
        $output->writeln("\n--- 测试5: 错误处理 ---");

        try {
            // 测试无效操作
            $this->redisManager->execute(function($redis) {
                // 尝试执行一个可能失败的操作
                $redis->eval("return redis.call('invalid_command')", 0);
            });

            $output->writeln('✗ 错误处理测试失败: 应该抛出异常');
        } catch (\Exception $e) {
            $output->writeln('✓ 错误处理测试成功: 正确捕获异常');
        }
    }

    /**
     * 输出最终监控报告
     * @param Output $output
     */
    protected function outputFinalReport(Output $output): void
    {
        $output->writeln("\n--- 最终监控报告 ---");

        try {
            $stats  = $this->redisManager->getStats();
            $report = $this->redisManager->getMonitoringReport();

            $output->writeln($report);

            // 输出详细统计
            $output->writeln("\n--- 详细统计信息 ---");
            $output->writeln('连接池大小: ' . ($stats['pool_size'] ?? 0));
            $output->writeln('当前连接数: ' . ($stats['current_count'] ?? 0));
            $output->writeln('可用连接数: ' . ($stats['available'] ?? 0));
            $output->writeln('等待连接数: ' . ($stats['waiting'] ?? 0));
            $output->writeln('总操作数: ' . ($stats['operation_stats']['total_operations'] ?? 0));
            $output->writeln('成功操作数: ' . ($stats['operation_stats']['successful_operations'] ?? 0));
            $output->writeln('失败操作数: ' . ($stats['operation_stats']['failed_operations'] ?? 0));
            $output->writeln('成功率: ' . ($stats['success_rate'] ?? 0) . '%');
            $output->writeln('平均执行时间: ' . ($stats['avg_execution_time_ms'] ?? 0) . 'ms');
        } catch (\Exception $e) {
            $output->writeln('✗ 获取监控报告异常: ' . $e->getMessage());
        }
    }
}
