<?php
declare(strict_types = 1);

namespace app\tenant\repository;

use app\tenant\model\Device as DeviceModel;
use app\tenant\service\DeviceStatusService;
use think\Model;

/**
 * 设备数据仓储类
 * <AUTHOR> <<EMAIL>>
 * @package app\tenant\repository
 */
class DeviceRepository extends BaseRepository
{
    public function __construct()
    {
        // 指定当前模型类名而非实例，避免条件累积问题
        parent::__construct(DeviceModel::class);
    }

    /**
     * 获取用户有权限管理的设备列表
     * @param array $params 查询参数
     * @param array $roleIds 用户角色ID数组
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @param bool $withOnlineStatus 是否获取在线状态
     * @return array
     */
    public function getUserDevices(array $params, array $roleIds, int $pageNo = 1, int $pageSize = 20, bool $withOnlineStatus = false): array
    {
        if (empty($roleIds)) {
            return [
                'total'    => 0,
                'list'     => [],
                'page'     => $pageNo,
                'pageSize' => $pageSize,
            ];
        }

        // 基础查询条件
        $query = $this->model->alias('w')
            ->join('tenant_role_device rw', 'rw.device_id = w.id AND rw.tenant_id = w.tenant_id')
            ->where([
                ['w.tenant_id', '=', $params['tenant_id'] ?? $this->getTenantId()],
                ['w.status', '=', 1],
                ['rw.role_id', 'in', $roleIds],
            ])
            ->field('DISTINCT w.*');

        // 关键字搜索
        if (!empty($params['keyword'])) {
            $query->where(function($query) use ($params) {
                $query->whereOr([
                    ['w.code', 'LIKE', "%{$params['keyword']}%"],
                    ['w.name', 'LIKE', "%{$params['keyword']}%"],
                    ['w.location', 'LIKE', "%{$params['keyword']}%"],
                ]);
            });
        }

        // 排序
        $query->order('w.id', 'ASC');

        // 分页
        $total = $query->count('DISTINCT w.id');
        $list  = $query->page($pageNo, $pageSize)->select();

        // 如果需要获取在线状态，并且有租户编码
        if ($list->isEmpty() === false && $withOnlineStatus && !empty($params['tenant_code'])) {
            $list = $this->getDeviceOnlineStatus($list->toArray(), $params['tenant_code']);
        }

        return [
            'total'    => $total,
            'list'     => $list,
            'page'     => $pageNo,
            'pageSize' => $pageSize,
        ];
    }

    /**
     * 获取设备列表
     * @param array $params 查询参数
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @param bool $withOnlineStatus 是否获取在线状态
     * @return array
     */
    public function getList(array $params, int $pageNo = 1, int $pageSize = 20, bool $withOnlineStatus = false): array
    {
        // 每次查询前清空条件
        //$this->model->clearConditions();

        // 设置基础条件 - 租户ID
        $this->setCondition([
            // 指定当前租户数据
            'tenant_id' => $params['tenant_id'] ?? $this->getTenantId(),
            // 指定非删除数据
            ['status', '>=', 0],
        ]);

        // 设置搜索条件
        if (!empty($params['keyword'])) {
            $this->setCondition([
                function($query) use ($params) {
                    $query->whereOr([
                        ['code', 'LIKE', "%{$params['keyword']}%"],
                        ['name', 'LIKE', "%{$params['keyword']}%"],
                        ['location', 'LIKE', "%{$params['keyword']}%"],
                    ]);
                },
            ]);
        }

        // 复用通用分页方法
        $result = $this->pageList($params, $pageNo, $pageSize);

        // 处理状态文本
        if (!empty($result['list'])) {
            $result['list'] = $result['list']->each(function($item) {
                $item->status_text = $this->getStatusText($item->status);

                return $item;
            });

            // 如果需要获取在线状态，并且有租户编码
            if ($withOnlineStatus && isset($params['tenant_code'])) {
                $result['list'] = $this->getDeviceOnlineStatus($result['list']->toArray(), $params['tenant_code']);
            }
        }

        return $result;
    }

    /**
     * 获取可分配的设备列表
     * 当传入角色ID时，返回未分配的设备和已分配给该角色的设备
     * 未传入角色ID时，只返回未分配的设备
     * @param array $params 查询参数
     * @param int|null $currentRoleId 当前角色ID(编辑时需要)
     * @param int $pageNo 页码
     * @param int $pageSize 每页数量
     * @param bool $withOnlineStatus 是否获取在线状态
     * @return array
     */
    public function getAssignableList(array $params, ?int $currentRoleId = null, int $pageNo = 1, int $pageSize = 20, bool $withOnlineStatus = false): array
    {
        // 获取查询构造器
        $query = $this->model->alias('w')
            ->leftJoin('tenant_role_device rw', 'rw.device_id = w.id AND rw.tenant_id = w.tenant_id');

        // 编辑场景排除当前角色
        if ($currentRoleId) {
            // 基础查询条件
            $query->where([
                ['w.tenant_id', '=', $params['tenant_id'] ?? $this->getTenantId()],
                ['w.status', '>=', 0],  // 只查询正常状态的设备
            ]);

            // 未分配的设备或者已分配给当前角色的设备
            $query->where(function($query) use ($currentRoleId) {
                $query->whereNull('rw.role_id')  // 未分配的设备
                      ->whereOr('rw.role_id', '=', $currentRoleId);  // 已分配给当前角色的设备
            });
        } else {
            // 基础查询条件
            $query->where([
                ['w.tenant_id', '=', $params['tenant_id'] ?? $this->getTenantId()],
                ['w.status', '>=', 0],  // 只查询正常状态的设备
            ])->whereNull('rw.role_id'); // 未分配的设备
        }

        // 关键字搜索
        if (!empty($params['keyword'])) {
            $query->where(function($query) use ($params) {
                $query->whereOr([
                    ['w.code', 'LIKE', "%{$params['keyword']}%"],
                    ['w.name', 'LIKE', "%{$params['keyword']}%"],
                    ['w.location', 'LIKE', "%{$params['keyword']}%"],
                ]);
            });
        }

        // 只选择设备表字段
        if ($currentRoleId) {
            // 添加是否已分配给当前角色的标记
            $query->field('w.*, IF(rw.role_id = ' . intval($currentRoleId) . ', 1, 0) as is_assigned');
        } else {
            $query->field('w.*');
        }

        $query->order('w.id', 'ASC');

        // 手动构建分页
        $total = $query->count();
        $list  = $query->page($pageNo, $pageSize)->select();

        // 如果需要获取在线状态，并且有租户编码
        if ($list->isEmpty() === false && $withOnlineStatus && !empty($params['tenant_code'])) {
            $list = $this->getDeviceOnlineStatus($list->toArray(), $params['tenant_code']);
        }

        return [
            'total'    => $total,
            'list'     => $list,
            'page'     => $pageNo,
            'pageSize' => $pageSize,
        ];
    }

    /**
     * 添加设备
     * @param array $params
     * @throws \Exception
     * @return \think\Model
     */
    public function addInfo(array $params): Model
    {
        $data = [
            'tenant_id' => intval($params['tenant_id'] ?? 0),
            'code'      => trim($params['code'] ?? ''),
            'name'      => trim($params['name'] ?? ''),
            'location'  => trim($params['location'] ?? ''),
            'depth'     => floatval($params['depth'] ?? ''),
            'level'     => intval($params['level'] ?? 0),
            'status'    => DeviceModel::STATUS_NORMAL,
        ];

        if (empty($data['tenant_id'])) {
            throw new \Exception('租户ID不能为空');
        }

        return $this->createData($data);
    }

    /**
     * 更新设备信息
     * @param mixed $condition 设备ID或数组查询条件
     * @param array $params
     * @throws \Exception
     * @return bool
     */
    public function updateInfo(mixed $condition, array $params): bool
    {
        $data = [
            'tenant_id' => intval($params['tenant_id'] ?? 0),
            'code'      => trim($params['code'] ?? ''),
            'name'      => trim($params['name'] ?? ''),
            'location'  => trim($params['location'] ?? ''),
            'depth'     => floatval($params['depth'] ?? ''),
            'level'     => intval($params['level'] ?? 0),
            'status'    => intval($params['status'] ?? 0),
        ];

        if (empty($data['tenant_id'])) {
            throw new \Exception('租户ID不能为空');
        }

        return $this->updateData($condition, $data);
    }

    /**
     * 更新设备状态
     * @param mixed $condition 设备ID或数组查询条件
     * @param int $status 状态值
     * @return bool
     */
    public function updateStatus(mixed $condition, int $status): bool
    {
        return $this->updateData($condition, ['status' => $status]);
    }

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public function getStatusText(int $status): string
    {
        return $this->model->getStatusText($status);
    }

    /**
     * 为设备列表添加在线状态信息
     * @param array $deviceList 设备列表
     * @param string $tenantCode 租户编码
     * @return array 添加了在线状态的设备列表
     */
    public function getDeviceOnlineStatus(array $deviceList, string $tenantCode): array
    {
        if (empty($deviceList)) {
            return $deviceList;
        }

        $deviceIds = [];

        foreach ($deviceList as $device) {
            $deviceIds[] = $device['id'];
        }

        // 批量获取设备状态
        $deviceStatusService = DeviceStatusService::getInstance();
        $deviceStatusList    = $deviceStatusService->batchGetDeviceStatus($deviceIds, $tenantCode);

        // 将设备状态添加到结果中
        foreach ($deviceList as &$device) {
            // 是否在线
            $device['online'] = $deviceStatusList[$device['id']]['online'] ?? false;
        }

        return $deviceList;
    }
}
