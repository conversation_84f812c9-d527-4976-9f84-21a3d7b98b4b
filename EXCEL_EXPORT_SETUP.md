# Excel导出功能安装指南

## 概述

本项目已实现了完善的设备历史数据导出功能，支持CSV和Excel两种格式。目前CSV导出功能可以直接使用，Excel导出功能需要安装额外的PHP扩展和依赖包。

## 功能特性

- ✅ **CSV导出**: 开箱即用，无需额外依赖
- ✅ **Excel导出**: 需要安装PhpSpreadsheet和相关PHP扩展
- ✅ **智能导出**: 自动检测环境，优先使用Excel，失败时降级到CSV
- ✅ **数据验证**: 完整的参数验证和错误处理
- ✅ **文件命名**: 自动生成包含设备ID和时间范围的文件名
- ✅ **内存保护**: 限制最大导出记录数，防止内存溢出

## 当前状态

### ✅ 已完成
- CSV导出功能（可直接使用）
- 智能导出功能（会自动降级到CSV）
- 完整的错误处理和日志记录
- 数据验证和安全检查

### ⚠️ 需要配置（Excel支持）
- 安装PHP扩展
- 安装PhpSpreadsheet库

## Excel支持安装步骤

### 1. 安装必需的PHP扩展

Excel导出功能需要以下PHP扩展：

```bash
# Windows (使用XAMPP/WAMP等)
# 在php.ini中启用以下扩展：
extension=fileinfo
extension=gd
extension=mbstring
extension=zip

# Linux (Ubuntu/Debian)
sudo apt-get install php-fileinfo php-gd php-mbstring php-zip

# Linux (CentOS/RHEL)
sudo yum install php-fileinfo php-gd php-mbstring php-zip
```

### 2. 安装PhpSpreadsheet库

```bash
# 在项目根目录执行
composer require phpoffice/phpspreadsheet
```

### 3. 验证安装

运行测试脚本验证功能：

```bash
php test_export.php
```

## API使用说明

### 导出设备历史数据

**接口地址**: `POST /tenant/device/historyDataExport`

**请求参数**:
```json
{
    "deviceId": "设备ID",
    "startTime": "2024-01-01 00:00:00",
    "endTime": "2024-01-01 23:59:59",
    "dataTypes": ["flow", "innerPressure", "outerPressure", "temperature", "valveOpening"],
    "interval": "10m",
    "format": "excel",
    "layer": "可选层级参数"
}
```

**参数说明**:
- `deviceId`: 设备ID（必填）
- `startTime`: 开始时间（必填）
- `endTime`: 结束时间（必填）
- `dataTypes`: 数据类型数组（必填）
- `interval`: 时间间隔，支持: 1m, 5m, 10m, 30m, 1h, 6h, 1d（可选，默认10m）
- `format`: 导出格式，支持: excel, csv（可选，默认excel）
- `layer`: 层级信息（可选）

**支持的数据类型**:
- `flow`: 流量 (L/min)
- `innerPressure`: 内压 (MPa)
- `outerPressure`: 外压 (MPa)
- `temperature`: 温度 (°C)
- `valveOpening`: 阀门开度 (%)

**响应**:
- 成功: 返回文件流，浏览器会自动下载
- 失败: 返回JSON错误信息

## 文件结构

```
app/
├── common/
│   └── service/
│       └── ExportService.php          # 导出服务类
└── tenant/
    └── controller/
        └── Device.php                  # 设备控制器（已更新）

test_export.php                        # 测试脚本
EXCEL_EXPORT_SETUP.md                  # 本文档
```

## 错误处理

系统会自动处理以下错误情况：

1. **参数验证错误**: 缺少必要参数、时间格式错误等
2. **环境检查**: 自动检测PHP扩展和依赖库
3. **内存保护**: 限制最大导出50,000条记录
4. **降级处理**: Excel导出失败时自动降级到CSV
5. **日志记录**: 所有错误都会记录到操作日志

## 性能优化建议

1. **大数据量导出**: 建议使用分页或时间分段导出
2. **服务器配置**: 适当调整PHP内存限制和执行时间
3. **缓存策略**: 对于重复查询可考虑添加缓存机制

## 故障排除

### 常见问题

1. **CSV导出正常，Excel导出失败**
   - 检查PHP扩展是否安装
   - 检查PhpSpreadsheet是否安装

2. **内存不足错误**
   - 减少导出时间范围
   - 增加PHP内存限制

3. **文件下载失败**
   - 检查浏览器设置
   - 检查服务器响应头

### 日志查看

导出操作的日志会记录在系统操作日志中，可以通过以下方式查看：
- 成功导出会记录基本信息
- 失败导出会记录详细错误信息

## 后续扩展

可以考虑添加以下功能：

1. **更多导出格式**: PDF、XML等
2. **模板支持**: 自定义Excel模板
3. **异步导出**: 大数据量异步处理
4. **邮件发送**: 导出完成后邮件通知
5. **云存储**: 导出文件上传到云存储
