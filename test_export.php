<?php
/**
 * 简单测试脚本 - 测试导出功能
 * 运行方式: php test_export.php
 */

require_once 'vendor/autoload.php';

use app\common\service\ExportService;

// 模拟测试数据
$testData = [
    [
        'time' => '2024-01-01 10:00:00',
        'flow' => 150.5,
        'innerPressure' => 12.34,
        'outerPressure' => 8.76,
        'temperature' => 45.2,
        'valveOpening' => 75.0
    ],
    [
        'time' => '2024-01-01 10:10:00',
        'flow' => 148.3,
        'innerPressure' => 12.28,
        'outerPressure' => 8.82,
        'temperature' => 45.8,
        'valveOpening' => 73.5
    ],
    [
        'time' => '2024-01-01 10:20:00',
        'flow' => 152.1,
        'innerPressure' => 12.41,
        'outerPressure' => 8.69,
        'temperature' => 44.9,
        'valveOpening' => 76.2
    ]
];

// 表头配置
$headers = [
    'time' => '时间',
    'flow' => '流量 (L/min)',
    'innerPressure' => '内压 (MPa)',
    'outerPressure' => '外压 (MPa)',
    'temperature' => '温度 (°C)',
    'valveOpening' => '阀门开度 (%)'
];

try {
    $exportService = new ExportService();
    
    echo "测试CSV导出功能...\n";
    
    // 测试CSV导出
    $csvResponse = $exportService->exportCsv($testData, $headers, 'test_device_data');
    echo "CSV导出成功！\n";
    echo "Content-Type: " . $csvResponse->getHeader('Content-Type') . "\n";
    echo "Content-Disposition: " . $csvResponse->getHeader('Content-Disposition') . "\n";
    echo "数据长度: " . strlen($csvResponse->getContent()) . " bytes\n\n";
    
    echo "测试智能导出功能...\n";
    
    // 测试智能导出（会自动降级到CSV，因为没有安装PhpSpreadsheet）
    $smartResponse = $exportService->smartExport($testData, $headers, 'test_device_smart', 'excel');
    echo "智能导出成功！\n";
    echo "Content-Type: " . $smartResponse->getHeader('Content-Type') . "\n";
    echo "Content-Disposition: " . $smartResponse->getHeader('Content-Disposition') . "\n";
    echo "数据长度: " . strlen($smartResponse->getContent()) . " bytes\n\n";
    
    // 尝试Excel导出（会抛出异常）
    echo "测试Excel导出功能...\n";
    try {
        $excelResponse = $exportService->exportExcel($testData, $headers, 'test_device_excel');
        echo "Excel导出成功！\n";
    } catch (Exception $e) {
        echo "Excel导出失败（预期）: " . $e->getMessage() . "\n";
    }
    
    echo "\n所有测试完成！\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
}
